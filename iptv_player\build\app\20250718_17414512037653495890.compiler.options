"-Xallow-no-source-files" "-classpath" "C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9a85168ee63393195cfa13f0fdb3be98\\transformed\\jetified-libs.jar;C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\shared_preferences_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e7379be8a94b6a3352ebf03be3b884ab\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.22.jar;C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\package_info_plus\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\path_provider_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\url_launcher_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\video_player_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\wakelock_plus\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a3f2904041d4afa222cbd82fa52e5d95\\transformed\\jetified-flutter_embedding_debug-1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4317354bb0b23ff945f7961364464a69\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2c4a85eef331a72bc176252703ea55a\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142efd8b909bd52f2d0042b95796c3aa\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d5b2d5702492805552364fc43374f12a\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\422ea64cefee41da643bbbfb78c8d64f\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3f173e81a67cfacacac50e6aa230230e\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1d955bc527cdd99ebe33890484a53e4\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f3c7004455fc9c62de36d6727c502fda\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f38d309cef5c24e99252d436f2bc9f27\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b30fbdd6f3aa94f92c1e50aad3efea20\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c7fb4613418c11690725e088accee135\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\afe4f4b2a2d12a5724b5626fce7a33ff\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\496ffad222368f9d1f9b2baaa96f2e69\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9020b0bb04d3436acc6c0105a020a37a\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2066c8f68bce80528353479346e2d02b\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0a2446df1be4d2941fa9bc64b225a611\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\279748029f5c590dc2a0b96367deac6b\\transformed\\jetified-media3-exoplayer-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\58587d48cb53174fd42546830018bf1d\\transformed\\jetified-media3-container-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f8f59e8527f5846742c0f6eca5bd675\\transformed\\jetified-media3-common-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\33f8de665389b135506e82cdd213b0ec\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\89ff450dd21663c0b92093067980a3e7\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59cd0b4dfef3994a944f8bf632b3c842\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\58ca8f9d4670286800d7b0c293c87aac\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.2.0\\34dbc21d203cc4d4d623ac572a21acd4ccd716af\\collection-1.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de966103171813304aa82f376f4d7c0c\\transformed\\jetified-annotation-jvm-1.9.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0d72283ce759c8256461f65e36a80d0\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8622d657d16b7db2e90b3a098bda9964\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\176bef61ca2a529540856c51c376f384\\transformed\\jetified-kotlin-stdlib-1.9.24.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\df8d250b85b9f8474475e63ea7e1f41e\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9c01398a7b009bbb9c0f739e7529a02\\transformed\\jetified-armeabi_v7a_debug-1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\440f32f206a4cd04415a8c7781965ec3\\transformed\\jetified-media3-ui-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0a27572a9e9d8c02b384bf2fa7c7de2\\transformed\\jetified-media3-datasource-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0110fb25dacb611d918be4a17c08643f\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\52e7a8078c8a20f91f291a8206bd0c2b\\transformed\\jetified-guava-33.0.0-android.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\985004bbb430895116d31cee49acd503\\transformed\\jetified-listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78aac12c79c5be331cd1775be7eaaf20\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5466ab4b956ae678ff71dce115e3d0a\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\43de2aba0ac347139df88f34d7e21c2c\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6652eef2f8a6affe4a62a3e669d98e74\\transformed\\jetified-failureaccess-1.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0cc51f94dc934516db934fc1e1167ea1\\transformed\\jetified-media3-database-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0cfc49693be777f1b23338e342cf9b1a\\transformed\\jetified-media3-decoder-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b14ae1298a789e5ec2988db254575d7\\transformed\\jetified-media3-extractor-1.4.1-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\android\\app\\src\\main\\kotlin\\com\\example\\iptv_player\\ExoPlayerActivity.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\android\\app\\src\\main\\kotlin\\com\\example\\iptv_player\\MainActivity.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\android\\app\\src\\main\\java\\io\\flutter\\plugins\\GeneratedPluginRegistrant.java"