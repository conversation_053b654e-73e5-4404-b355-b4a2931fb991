import '../models/channel.dart';

class SampleData {
  static List<Channel> get featuredChannels => [
    Channel(
      id: '1',
      name: 'Action Cinema',
      streamUrl: 'https://example.com/stream1',
      logoUrl: 'https://picsum.photos/300/450?random=1',
      description: 'The best action movies and blockbusters from Hollywood and around the world. Non-stop entertainment with the biggest stars.',
      category: 'Movies',
    ),
    Channel(
      id: '2',
      name: 'Sports Live HD',
      streamUrl: 'https://example.com/stream2',
      logoUrl: 'https://picsum.photos/300/450?random=2',
      description: 'Live sports coverage including football, basketball, tennis, and more. Never miss your favorite team in action.',
      category: 'Sports',
    ),
    Channel(
      id: '3',
      name: 'News 24/7',
      streamUrl: 'https://example.com/stream3',
      logoUrl: 'https://picsum.photos/300/450?random=3',
      description: 'Breaking news and current affairs from around the globe. Stay informed with the latest updates.',
      category: 'News',
    ),
  ];

  static List<ChannelCategory> get categories => [
    ChannelCategory(
      id: 'movies',
      name: 'Movies & Series',
      channels: [
        Channel(
          id: '4',
          name: 'Action Cinema',
          streamUrl: 'https://example.com/stream4',
          logoUrl: 'https://picsum.photos/300/450?random=4',
          category: 'Movies',
        ),
        Channel(
          id: '5',
          name: 'Comedy Central',
          streamUrl: 'https://example.com/stream5',
          logoUrl: 'https://picsum.photos/300/450?random=5',
          category: 'Movies',
        ),
        Channel(
          id: '6',
          name: 'Drama Plus',
          streamUrl: 'https://example.com/stream6',
          logoUrl: 'https://picsum.photos/300/450?random=6',
          category: 'Movies',
        ),
        Channel(
          id: '7',
          name: 'Horror Night',
          streamUrl: 'https://example.com/stream7',
          logoUrl: 'https://picsum.photos/300/450?random=7',
          category: 'Movies',
        ),
        Channel(
          id: '8',
          name: 'Sci-Fi World',
          streamUrl: 'https://example.com/stream8',
          logoUrl: 'https://picsum.photos/300/450?random=8',
          category: 'Movies',
        ),
        Channel(
          id: '9',
          name: 'Romance Films',
          streamUrl: 'https://example.com/stream9',
          logoUrl: 'https://picsum.photos/300/450?random=9',
          category: 'Movies',
        ),
      ],
    ),
    ChannelCategory(
      id: 'sports',
      name: 'Sports',
      channels: [
        Channel(
          id: '10',
          name: 'Football Live',
          streamUrl: 'https://example.com/stream10',
          logoUrl: 'https://picsum.photos/300/450?random=10',
          category: 'Sports',
        ),
        Channel(
          id: '11',
          name: 'Basketball Pro',
          streamUrl: 'https://example.com/stream11',
          logoUrl: 'https://picsum.photos/300/450?random=11',
          category: 'Sports',
        ),
        Channel(
          id: '12',
          name: 'Tennis Court',
          streamUrl: 'https://example.com/stream12',
          logoUrl: 'https://picsum.photos/300/450?random=12',
          category: 'Sports',
        ),
        Channel(
          id: '13',
          name: 'Racing Speed',
          streamUrl: 'https://example.com/stream13',
          logoUrl: 'https://picsum.photos/300/450?random=13',
          category: 'Sports',
        ),
        Channel(
          id: '14',
          name: 'Boxing Arena',
          streamUrl: 'https://example.com/stream14',
          logoUrl: 'https://picsum.photos/300/450?random=14',
          category: 'Sports',
        ),
      ],
    ),
    ChannelCategory(
      id: 'entertainment',
      name: 'Entertainment',
      channels: [
        Channel(
          id: '15',
          name: 'Music Videos',
          streamUrl: 'https://example.com/stream15',
          logoUrl: 'https://picsum.photos/300/450?random=15',
          category: 'Entertainment',
        ),
        Channel(
          id: '16',
          name: 'Reality Shows',
          streamUrl: 'https://example.com/stream16',
          logoUrl: 'https://picsum.photos/300/450?random=16',
          category: 'Entertainment',
        ),
        Channel(
          id: '17',
          name: 'Talk Shows',
          streamUrl: 'https://example.com/stream17',
          logoUrl: 'https://picsum.photos/300/450?random=17',
          category: 'Entertainment',
        ),
        Channel(
          id: '18',
          name: 'Game Shows',
          streamUrl: 'https://example.com/stream18',
          logoUrl: 'https://picsum.photos/300/450?random=18',
          category: 'Entertainment',
        ),
        Channel(
          id: '19',
          name: 'Variety Shows',
          streamUrl: 'https://example.com/stream19',
          logoUrl: 'https://picsum.photos/300/450?random=19',
          category: 'Entertainment',
        ),
      ],
    ),
    ChannelCategory(
      id: 'kids',
      name: 'Kids & Family',
      channels: [
        Channel(
          id: '20',
          name: 'Cartoons',
          streamUrl: 'https://example.com/stream20',
          logoUrl: 'https://picsum.photos/300/450?random=20',
          category: 'Kids',
        ),
        Channel(
          id: '21',
          name: 'Educational',
          streamUrl: 'https://example.com/stream21',
          logoUrl: 'https://picsum.photos/300/450?random=21',
          category: 'Kids',
        ),
        Channel(
          id: '22',
          name: 'Family Fun',
          streamUrl: 'https://example.com/stream22',
          logoUrl: 'https://picsum.photos/300/450?random=22',
          category: 'Kids',
        ),
        Channel(
          id: '23',
          name: 'Adventure Kids',
          streamUrl: 'https://example.com/stream23',
          logoUrl: 'https://picsum.photos/300/450?random=23',
          category: 'Kids',
        ),
      ],
    ),
    ChannelCategory(
      id: 'news',
      name: 'News & Documentary',
      channels: [
        Channel(
          id: '24',
          name: 'World News',
          streamUrl: 'https://example.com/stream24',
          logoUrl: 'https://picsum.photos/300/450?random=24',
          category: 'News',
        ),
        Channel(
          id: '25',
          name: 'Local News',
          streamUrl: 'https://example.com/stream25',
          logoUrl: 'https://picsum.photos/300/450?random=25',
          category: 'News',
        ),
        Channel(
          id: '26',
          name: 'Documentaries',
          streamUrl: 'https://example.com/stream26',
          logoUrl: 'https://picsum.photos/300/450?random=26',
          category: 'News',
        ),
        Channel(
          id: '27',
          name: 'History Channel',
          streamUrl: 'https://example.com/stream27',
          logoUrl: 'https://picsum.photos/300/450?random=27',
          category: 'News',
        ),
      ],
    ),
  ];
}
