#Fri Jul 18 20:45:52 EET 2025
com.example.iptv_player.app-main-37\:/drawable-v21/launch_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v21_launch_background.xml.flat
com.example.iptv_player.app-main-37\:/drawable/launch_logo.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_launch_logo.xml.flat
com.example.iptv_player.app-main-37\:/drawable/launch_logo_vector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_launch_logo_vector.xml.flat
com.example.iptv_player.app-main-37\:/drawable/loading_dots.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_loading_dots.xml.flat
com.example.iptv_player.app-main-37\:/drawable/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo.png.flat
com.example.iptv_player.app-main-37\:/drawable/logo_small.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo_small.xml.flat
com.example.iptv_player.app-main-37\:/drawable/logo_splash.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo_splash.xml.flat
com.example.iptv_player.app-main-37\:/drawable/splash_screen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_splash_screen.xml.flat
com.example.iptv_player.app-main-37\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-37\:/mipmap-hdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_logo.png.flat
com.example.iptv_player.app-main-37\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-37\:/mipmap-mdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_logo.png.flat
com.example.iptv_player.app-main-37\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-37\:/mipmap-xhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_logo.png.flat
com.example.iptv_player.app-main-37\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-37\:/mipmap-xxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_logo.png.flat
com.example.iptv_player.app-main-37\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-37\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_logo.png.flat
com.example.iptv_player.app-main-37\:/xml/network_security_config.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
com.example.iptv_player.app-main-40\:/drawable-v21/launch_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v21_launch_background.xml.flat
com.example.iptv_player.app-main-40\:/drawable/gradient_bottom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_bottom.xml.flat
com.example.iptv_player.app-main-40\:/drawable/gradient_top.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_top.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_arrow_back.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_back.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_forward_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_forward_10.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_replay_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_replay_10.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_settings.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.example.iptv_player.app-main-40\:/drawable/launch_logo.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_launch_logo.xml.flat
com.example.iptv_player.app-main-40\:/drawable/launch_logo_vector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_launch_logo_vector.xml.flat
com.example.iptv_player.app-main-40\:/drawable/loading_dots.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_loading_dots.xml.flat
com.example.iptv_player.app-main-40\:/drawable/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo.png.flat
com.example.iptv_player.app-main-40\:/drawable/logo_small.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo_small.xml.flat
com.example.iptv_player.app-main-40\:/drawable/logo_splash.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo_splash.xml.flat
com.example.iptv_player.app-main-40\:/drawable/splash_screen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_splash_screen.xml.flat
com.example.iptv_player.app-main-40\:/layout/activity_exoplayer.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_exoplayer.xml.flat
com.example.iptv_player.app-main-40\:/layout/custom_player_control_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_custom_player_control_view.xml.flat
com.example.iptv_player.app-main-40\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-hdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-mdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/xml/network_security_config.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
com.example.iptv_player.app-main-41\:/drawable-v21/launch_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v21_launch_background.xml.flat
com.example.iptv_player.app-main-41\:/drawable/launch_logo.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_launch_logo.xml.flat
com.example.iptv_player.app-main-41\:/drawable/launch_logo_vector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_launch_logo_vector.xml.flat
com.example.iptv_player.app-main-41\:/drawable/loading_dots.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_loading_dots.xml.flat
com.example.iptv_player.app-main-41\:/drawable/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo.png.flat
com.example.iptv_player.app-main-41\:/drawable/logo_small.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo_small.xml.flat
com.example.iptv_player.app-main-41\:/drawable/logo_splash.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo_splash.xml.flat
com.example.iptv_player.app-main-41\:/drawable/splash_screen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_splash_screen.xml.flat
com.example.iptv_player.app-main-41\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-41\:/mipmap-hdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_logo.png.flat
com.example.iptv_player.app-main-41\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-41\:/mipmap-mdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_logo.png.flat
com.example.iptv_player.app-main-41\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-41\:/mipmap-xhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_logo.png.flat
com.example.iptv_player.app-main-41\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-41\:/mipmap-xxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_logo.png.flat
com.example.iptv_player.app-main-41\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-41\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_logo.png.flat
com.example.iptv_player.app-main-41\:/xml/network_security_config.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
