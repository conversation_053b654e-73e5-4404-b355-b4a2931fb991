#Fri Jul 18 22:04:39 EET 2025
com.example.iptv_player.app-main-40\:/color/button_tint_selector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_button_tint_selector.xml.flat
com.example.iptv_player.app-main-40\:/drawable-v21/launch_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v21_launch_background.xml.flat
com.example.iptv_player.app-main-40\:/drawable/button_selector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_selector.xml.flat
com.example.iptv_player.app-main-40\:/drawable/gradient_bottom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_bottom.xml.flat
com.example.iptv_player.app-main-40\:/drawable/gradient_top.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_top.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_arrow_back.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_back.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_audiotrack.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_audiotrack.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_forward_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_forward_10.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_fullscreen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fullscreen.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_hd.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_hd.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_replay_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_replay_10.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_settings.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_skip_next.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_skip_next.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_skip_previous.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_skip_previous.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_subtitles.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_subtitles.xml.flat
com.example.iptv_player.app-main-40\:/drawable/ic_volume_up.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_volume_up.xml.flat
com.example.iptv_player.app-main-40\:/drawable/launch_logo.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_launch_logo.xml.flat
com.example.iptv_player.app-main-40\:/drawable/launch_logo_vector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_launch_logo_vector.xml.flat
com.example.iptv_player.app-main-40\:/drawable/loading_dots.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_loading_dots.xml.flat
com.example.iptv_player.app-main-40\:/drawable/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo.png.flat
com.example.iptv_player.app-main-40\:/drawable/logo_small.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo_small.xml.flat
com.example.iptv_player.app-main-40\:/drawable/logo_splash.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo_splash.xml.flat
com.example.iptv_player.app-main-40\:/drawable/splash_screen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_splash_screen.xml.flat
com.example.iptv_player.app-main-40\:/layout/activity_exoplayer.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_exoplayer.xml.flat
com.example.iptv_player.app-main-40\:/layout/custom_player_control_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_custom_player_control_view.xml.flat
com.example.iptv_player.app-main-40\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-hdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-mdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-40\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_logo.png.flat
com.example.iptv_player.app-main-40\:/xml/network_security_config.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
