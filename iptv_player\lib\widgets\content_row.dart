import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../theme/app_theme.dart';
import '../models/channel.dart';
import 'simple_tv_focus.dart';

class ContentRow extends StatefulWidget {
  final String title;
  final List<Channel> channels;
  final Function(Channel) onChannelPressed;

  const ContentRow({
    Key? key,
    required this.title,
    required this.channels,
    required this.onChannelPressed,
  }) : super(key: key);

  @override
  State<ContentRow> createState() => _ContentRowState();
}

class _ContentRowState extends State<ContentRow> {
  final ScrollController _scrollController = ScrollController();
  int _focusedIndex = 0;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onChannelFocused(int index) {
    setState(() {
      _focusedIndex = index;
    });

    // Auto-scroll to keep focused item visible
    if (_scrollController.hasClients) {
      final itemWidth = 156.0; // 140 + 16 padding
      final scrollOffset = (index * itemWidth) - (MediaQuery.of(context).size.width / 2) + (itemWidth / 2);

      _scrollController.animateTo(
        scrollOffset.clamp(0.0, _scrollController.position.maxScrollExtent),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 60),
            child: Text(
              widget.title,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Horizontal List
          SizedBox(
            height: 200,
            child: ListView.builder(
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 60),
              itemCount: widget.channels.length,
              itemBuilder: (context, index) {
                final channel = widget.channels[index];
                return Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: SimpleChannelCard(
                    channel: channel,
                    onPressed: () => widget.onChannelPressed(channel),
                    autofocus: index == 0, // First item gets autofocus
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class SimpleChannelCard extends StatelessWidget {
  final Channel channel;
  final VoidCallback onPressed;
  final bool autofocus;

  const SimpleChannelCard({
    Key? key,
    required this.channel,
    required this.onPressed,
    this.autofocus = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SimpleTVFocus(
      autofocus: autofocus,
      onPressed: onPressed,
      child: Container(
        width: 140,
        height: 200,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Channel Thumbnail
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: channel.logoUrl != null
                      ? CachedNetworkImage(
                          imageUrl: channel.logoUrl!,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: AppTheme.cardBackground,
                            child: const Center(
                              child: CircularProgressIndicator(
                                color: AppTheme.primaryRed,
                                strokeWidth: 2,
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: AppTheme.cardBackground,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.tv,
                                  size: 40,
                                  color: AppTheme.textSecondary,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  channel.name,
                                  style: const TextStyle(
                                    color: AppTheme.textSecondary,
                                    fontSize: 12,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        )
                      : Container(
                          color: AppTheme.cardBackground,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.tv,
                                size: 40,
                                color: AppTheme.textSecondary,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                channel.name,
                                style: const TextStyle(
                                  color: AppTheme.textSecondary,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                ),
              ),
            ),

            const SizedBox(height: 8),

            // Channel Name
            Text(
              channel.name,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

// Focus change listener for the card
class _FocusableChannelCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onFocusChange;

  const _FocusableChannelCard({
    required this.child,
    required this.onFocusChange,
  });

  @override
  State<_FocusableChannelCard> createState() => _FocusableChannelCardState();
}

class _FocusableChannelCardState extends State<_FocusableChannelCard> {
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        widget.onFocusChange();
      }
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      child: widget.child,
    );
  }
}
