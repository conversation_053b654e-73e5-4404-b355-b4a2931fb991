package com.example.iptv_player

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.ui.PlayerView
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector
import androidx.media3.exoplayer.trackselection.AdaptiveTrackSelection

@UnstableApi
class ExoPlayerActivity : Activity() {
    
    private var player: ExoPlayer? = null
    private var playerView: PlayerView? = null
    private var streamUrl: String? = null
    private var movieTitle: String? = null
    
    companion object {
        const val EXTRA_STREAM_URL = "stream_url"
        const val EXTRA_MOVIE_TITLE = "movie_title"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Make fullscreen and keep screen on
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // Hide system UI for immersive experience
        hideSystemUI()
        
        // Get intent data
        streamUrl = intent.getStringExtra(EXTRA_STREAM_URL)
        movieTitle = intent.getStringExtra(EXTRA_MOVIE_TITLE)
        
        if (streamUrl == null) {
            Toast.makeText(this, "No stream URL provided", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        // Set up the layout
        setContentView(R.layout.activity_exoplayer)
        
        // Initialize player view
        playerView = findViewById(R.id.player_view)

        // Set movie title in the UI
        val titleView = findViewById<TextView>(R.id.exo_title)
        titleView?.text = movieTitle

        // Initialize ExoPlayer
        initializePlayer()
    }
    
    private fun initializePlayer() {
        try {
            // Create adaptive track selection factory
            val trackSelectionFactory = AdaptiveTrackSelection.Factory()
            val trackSelector = DefaultTrackSelector(this, trackSelectionFactory)
            
            // Configure track selector for better compatibility
            trackSelector.setParameters(
                trackSelector.buildUponParameters()
                    .setMaxVideoSizeSd() // Limit to SD resolution for compatibility
                    .setForceLowestBitrate(false)
                    .setAllowVideoMixedMimeTypeAdaptiveness(true)
                    .setAllowAudioMixedMimeTypeAdaptiveness(true)
            )
            
            // Create ExoPlayer instance with optimized settings
            player = ExoPlayer.Builder(this)
                .setTrackSelector(trackSelector)
                .build()
            
            // Set up player view with immediate playback
            playerView?.player = player
            playerView?.useController = true
            playerView?.controllerAutoShow = false  // Don't auto-show, we'll control it
            playerView?.controllerHideOnTouch = false
            playerView?.showController()  // Show controls immediately
            
            // Create media source based on URL type
            val mediaSource = createMediaSource(streamUrl!!)
            
            // Set up player listeners
            player?.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    when (playbackState) {
                        Player.STATE_READY -> {
                            // Start playback immediately when ready
                            player?.play()
                            // Don't show popup message - removed Toast
                        }
                        Player.STATE_BUFFERING -> {
                            // Show buffering indicator if needed
                        }
                        Player.STATE_ENDED -> {
                            finish()
                        }
                    }
                }
                
                override fun onPlayerError(error: PlaybackException) {
                    val errorMessage = when (error.errorCode) {
                        PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED -> 
                            "Network connection failed. Check your internet connection."
                        PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT -> 
                            "Connection timeout. Please try again."
                        PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED -> 
                            "Video format not supported."
                        PlaybackException.ERROR_CODE_DECODER_INIT_FAILED -> 
                            "Video decoder initialization failed. This video format may not be supported on your device."
                        else -> "Playback error: ${error.message}"
                    }
                    
                    Toast.makeText(this@ExoPlayerActivity, errorMessage, Toast.LENGTH_LONG).show()
                    finish()
                }
            })
            
            // Prepare and start playback immediately
            player?.setMediaSource(mediaSource)
            player?.prepare()
            player?.playWhenReady = true

            // Force immediate playback
            player?.play()
            
        } catch (e: Exception) {
            Toast.makeText(this, "Error initializing player: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }
    
    private fun createMediaSource(url: String): MediaSource {
        // Create HTTP data source factory with IPTV-optimized headers
        val dataSourceFactory = DefaultHttpDataSource.Factory()
            .setUserAgent("ExoPlayerLib/2.18.1 (Linux; Android 10; Android TV)")
            .setConnectTimeoutMs(30000)
            .setReadTimeoutMs(30000)
            .setAllowCrossProtocolRedirects(true)
        
        val uri = Uri.parse(url)
        val mediaItem = MediaItem.fromUri(uri)

        // Use Progressive Media Source for all formats (MP4, etc.)
        // This handles most IPTV streams including redirected URLs
        return ProgressiveMediaSource.Factory(dataSourceFactory)
            .createMediaSource(mediaItem)
    }
    
    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_FULLSCREEN
        )
    }
    
    override fun onStart() {
        super.onStart()
        if (player == null) {
            initializePlayer()
        }
    }
    
    override fun onResume() {
        super.onResume()
        hideSystemUI()
        player?.playWhenReady = true
    }
    
    override fun onPause() {
        super.onPause()
        player?.playWhenReady = false
    }
    
    override fun onStop() {
        super.onStop()
        releasePlayer()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }
    
    private fun releasePlayer() {
        player?.release()
        player = null
    }
    
    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }
}
