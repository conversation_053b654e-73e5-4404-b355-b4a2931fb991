package com.example.iptv_player

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.ui.PlayerView
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector
import androidx.media3.exoplayer.trackselection.AdaptiveTrackSelection
import android.os.Handler
import android.os.Looper
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

@UnstableApi
class ExoPlayerActivity : Activity() {
    
    private var player: ExoPlayer? = null
    private var playerView: PlayerView? = null
    private var streamUrl: String? = null
    private var movieTitle: String? = null
    private var movieId: String? = null
    private var movieThumbnail: String? = null
    private var resumePosition: Long = 0
    private var progressHandler: Handler? = null
    private var progressRunnable: Runnable? = null
    private var lastSavedPosition: Long = 0
    
    companion object {
        const val EXTRA_STREAM_URL = "stream_url"
        const val EXTRA_MOVIE_TITLE = "movie_title"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Make fullscreen and keep screen on
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // Hide system UI for immersive experience
        hideSystemUI()
        
        // Get intent data
        streamUrl = intent.getStringExtra(EXTRA_STREAM_URL)
        movieTitle = intent.getStringExtra(EXTRA_MOVIE_TITLE)
        movieId = intent.getStringExtra("movie_id")
        movieThumbnail = intent.getStringExtra("movie_thumbnail")
        resumePosition = intent.getLongExtra("resume_position", 0L)
        
        if (streamUrl == null) {
            Toast.makeText(this, "No stream URL provided", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        // Set up the layout
        setContentView(R.layout.activity_exoplayer)
        
        // Initialize player view
        playerView = findViewById(R.id.player_view)

        // Set movie title in the UI
        val titleView = findViewById<TextView>(R.id.exo_title)
        titleView?.text = movieTitle

        // Set up back button
        val backButton = findViewById<ImageButton>(R.id.exo_back)
        backButton?.setOnClickListener {
            finish()
        }

        // Initialize ExoPlayer
        initializePlayer()
    }
    
    private fun initializePlayer() {
        try {
            // Create adaptive track selection factory
            val trackSelectionFactory = AdaptiveTrackSelection.Factory()
            val trackSelector = DefaultTrackSelector(this, trackSelectionFactory)
            
            // Configure track selector for better compatibility
            trackSelector.setParameters(
                trackSelector.buildUponParameters()
                    .setMaxVideoSizeSd() // Limit to SD resolution for compatibility
                    .setForceLowestBitrate(false)
                    .setAllowVideoMixedMimeTypeAdaptiveness(true)
                    .setAllowAudioMixedMimeTypeAdaptiveness(true)
            )
            
            // Create ExoPlayer instance with optimized settings
            player = ExoPlayer.Builder(this)
                .setTrackSelector(trackSelector)
                .build()
            
            // Set up player view with only our custom controls
            playerView?.player = player
            playerView?.useController = true
            playerView?.controllerAutoShow = true  // Auto-show controls
            playerView?.controllerHideOnTouch = true  // Hide on touch
            playerView?.controllerShowTimeoutMs = 5000  // Hide after 5 seconds

            // Enable ExoPlayer controls to work with our custom layout
            playerView?.setShowRewindButton(true)
            playerView?.setShowFastForwardButton(true)
            playerView?.setShowPreviousButton(true)
            playerView?.setShowNextButton(true)

            playerView?.showController()  // Show controls initially

            // Set up custom control click handlers
            setupCustomControls()

            // Create media source based on URL type
            val mediaSource = createMediaSource(streamUrl!!)
            
            // Set up player listeners
            player?.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    when (playbackState) {
                        Player.STATE_READY -> {
                            // Seek to resume position if provided
                            if (resumePosition > 0) {
                                player?.seekTo(resumePosition)
                                println("🎬 Resuming playback at position: ${resumePosition}ms")
                            }
                            // Start playback immediately when ready
                            player?.play()
                            // Don't show popup message - removed Toast
                        }
                        Player.STATE_BUFFERING -> {
                            // Show buffering indicator if needed
                        }
                        Player.STATE_ENDED -> {
                            finish()
                        }
                    }
                }
                
                override fun onPlayerError(error: PlaybackException) {
                    val errorMessage = when (error.errorCode) {
                        PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED -> 
                            "Network connection failed. Check your internet connection."
                        PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT -> 
                            "Connection timeout. Please try again."
                        PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED -> 
                            "Video format not supported."
                        PlaybackException.ERROR_CODE_DECODER_INIT_FAILED -> 
                            "Video decoder initialization failed. This video format may not be supported on your device."
                        else -> "Playback error: ${error.message}"
                    }
                    
                    Toast.makeText(this@ExoPlayerActivity, errorMessage, Toast.LENGTH_LONG).show()
                    finish()
                }
            })
            
            // Prepare and start playback immediately
            player?.setMediaSource(mediaSource)
            player?.prepare()
            player?.playWhenReady = true

            // Force immediate playback
            player?.play()

            // Start progress tracking
            startProgressTracking()

        } catch (e: Exception) {
            Toast.makeText(this, "Error initializing player: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }
    
    private fun createMediaSource(url: String): MediaSource {
        // Create HTTP data source factory with IPTV-optimized headers
        val dataSourceFactory = DefaultHttpDataSource.Factory()
            .setUserAgent("ExoPlayerLib/2.18.1 (Linux; Android 10; Android TV)")
            .setConnectTimeoutMs(30000)
            .setReadTimeoutMs(30000)
            .setAllowCrossProtocolRedirects(true)
        
        val uri = Uri.parse(url)
        val mediaItem = MediaItem.fromUri(uri)

        // Use Progressive Media Source for all formats (MP4, etc.)
        // This handles most IPTV streams including redirected URLs
        return ProgressiveMediaSource.Factory(dataSourceFactory)
            .createMediaSource(mediaItem)
    }
    
    private fun setupCustomControls() {
        // Find custom control buttons
        val backButton = findViewById<ImageButton>(R.id.exo_back)
        val prevButton = findViewById<ImageButton>(R.id.exo_prev)
        val rewButton = findViewById<ImageButton>(R.id.exo_rew)
        val ffwdButton = findViewById<ImageButton>(R.id.exo_ffwd)
        val nextButton = findViewById<ImageButton>(R.id.exo_next)
        val subtitleButton = findViewById<ImageButton>(R.id.exo_subtitle)
        val fullscreenButton = findViewById<ImageButton>(R.id.exo_fullscreen)
        val settingsButton = findViewById<ImageButton>(R.id.exo_settings)

        // Set up click listeners
        backButton?.setOnClickListener {
            finish()
        }

        prevButton?.setOnClickListener {
            // Previous episode/chapter functionality
            player?.seekToPrevious()
        }

        rewButton?.setOnClickListener {
            // Rewind 10 seconds
            val currentPosition = player?.currentPosition ?: 0
            val newPosition = maxOf(0, currentPosition - 10000) // 10 seconds in milliseconds
            player?.seekTo(newPosition)
        }

        ffwdButton?.setOnClickListener {
            // Forward 10 seconds
            val currentPosition = player?.currentPosition ?: 0
            val duration = player?.duration ?: 0
            val newPosition = minOf(duration, currentPosition + 10000) // 10 seconds in milliseconds
            player?.seekTo(newPosition)
        }

        nextButton?.setOnClickListener {
            // Next episode/chapter functionality
            player?.seekToNext()
        }

        subtitleButton?.setOnClickListener {
            // Toggle subtitles functionality
            // TODO: Implement subtitle toggle
        }

        fullscreenButton?.setOnClickListener {
            // Fullscreen toggle functionality
            // TODO: Implement fullscreen toggle
        }

        settingsButton?.setOnClickListener {
            // Settings functionality
            // TODO: Implement settings menu
        }
    }

    private fun startProgressTracking() {
        progressHandler = Handler(Looper.getMainLooper())
        progressRunnable = object : Runnable {
            override fun run() {
                player?.let { player ->
                    val currentPosition = player.currentPosition
                    val duration = player.duration

                    // Save progress every 10 seconds and if position changed significantly
                    if (duration > 0 && currentPosition > 0 &&
                        Math.abs(currentPosition - lastSavedPosition) > 10000) {

                        val progressPercent = (currentPosition.toFloat() / duration.toFloat() * 100).toInt()

                        // Only save if progress is between 5% and 95% (not at beginning or end)
                        if (progressPercent in 5..95) {
                            saveContinueWatchingProgress(currentPosition, duration)
                            lastSavedPosition = currentPosition
                        }
                    }
                }

                // Schedule next update in 5 seconds
                progressHandler?.postDelayed(this, 5000)
            }
        }
        progressHandler?.post(progressRunnable!!)
    }

    private fun saveContinueWatchingProgress(position: Long, duration: Long) {
        movieId?.let { id ->
            movieTitle?.let { title ->
                // Create continue watching data
                val continueWatchingData = mapOf(
                    "movie_id" to id,
                    "movie_title" to title,
                    "movie_thumbnail" to (movieThumbnail ?: ""),
                    "current_position" to position,
                    "total_duration" to duration,
                    "progress_percent" to ((position.toFloat() / duration.toFloat()) * 100).toInt(),
                    "last_watched" to System.currentTimeMillis()
                )

                // Send to Flutter via method channel
                val flutterEngine = io.flutter.embedding.engine.FlutterEngineCache
                    .getInstance().get("my_engine_id")

                flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                    val channel = MethodChannel(messenger, "com.example.iptv_player/continue_watching")
                    channel.invokeMethod("saveContinueWatching", continueWatchingData)
                }
            }
        }
    }

    private fun stopProgressTracking() {
        progressRunnable?.let { runnable ->
            progressHandler?.removeCallbacks(runnable)
        }
        progressHandler = null
        progressRunnable = null
    }

    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_FULLSCREEN
        )
    }
    
    override fun onStart() {
        super.onStart()
        if (player == null) {
            initializePlayer()
        }
    }
    
    override fun onResume() {
        super.onResume()
        hideSystemUI()
        player?.playWhenReady = true
    }
    
    override fun onPause() {
        super.onPause()
        player?.playWhenReady = false
    }
    
    override fun onStop() {
        super.onStop()
        releasePlayer()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopProgressTracking()
        releasePlayer()
    }
    
    private fun releasePlayer() {
        player?.release()
        player = null
    }
    
    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }
}
