{"logs": [{"outputFile": "com.example.iptv_player.app-mergeDebugResources-43:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\android\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "3,15", "startColumns": "4,4", "startOffsets": "175,886", "endLines": "8,18", "endColumns": "12,12", "endOffsets": "537,1096"}, "to": {"startLines": "2,7", "startColumns": "4,4", "startOffsets": "55,290", "endLines": "6,10", "endColumns": "12,12", "endOffsets": "285,497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "502,572,656,740,836,938,1040,1134", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "567,651,735,831,933,1035,1129,1218"}}]}]}