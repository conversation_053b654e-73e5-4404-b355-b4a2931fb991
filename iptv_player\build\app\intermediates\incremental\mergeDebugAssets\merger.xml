<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="org.videolan.android:libvlc-all:3.6.0-eap14" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets"><file name="hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\hrtfs\dodeca_and_7channel_3DSL_HRTF.sofa"/><file name="lua/meta/art/00_musicbrainz.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\meta\art\00_musicbrainz.lua"/><file name="lua/meta/art/01_googleimage.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\meta\art\01_googleimage.lua"/><file name="lua/meta/art/02_frenchtv.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\meta\art\02_frenchtv.lua"/><file name="lua/meta/art/03_lastfm.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\meta\art\03_lastfm.lua"/><file name="lua/meta/reader/filename.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\meta\reader\filename.lua"/><file name="lua/modules/common.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\modules\common.lua"/><file name="lua/modules/dkjson.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\modules\dkjson.lua"/><file name="lua/modules/sandbox.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\modules\sandbox.lua"/><file name="lua/modules/simplexml.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\modules\simplexml.lua"/><file name="lua/playlist/anevia_streams.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\anevia_streams.lua"/><file name="lua/playlist/anevia_xml.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\anevia_xml.lua"/><file name="lua/playlist/appletrailers.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\appletrailers.lua"/><file name="lua/playlist/bbc_co_uk.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\bbc_co_uk.lua"/><file name="lua/playlist/break.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\break.lua"/><file name="lua/playlist/cue.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\cue.lua"/><file name="lua/playlist/dailymotion.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\dailymotion.lua"/><file name="lua/playlist/extreme.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\extreme.lua"/><file name="lua/playlist/france2.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\france2.lua"/><file name="lua/playlist/jamendo.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\jamendo.lua"/><file name="lua/playlist/katsomo.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\katsomo.lua"/><file name="lua/playlist/koreus.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\koreus.lua"/><file name="lua/playlist/lelombrik.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\lelombrik.lua"/><file name="lua/playlist/liveleak.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\liveleak.lua"/><file name="lua/playlist/metacafe.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\metacafe.lua"/><file name="lua/playlist/mpora.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\mpora.lua"/><file name="lua/playlist/newgrounds.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\newgrounds.lua"/><file name="lua/playlist/pinkbike.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\pinkbike.lua"/><file name="lua/playlist/rockbox_fm_presets.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\rockbox_fm_presets.lua"/><file name="lua/playlist/soundcloud.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\soundcloud.lua"/><file name="lua/playlist/twitch.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\twitch.lua"/><file name="lua/playlist/vimeo.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\vimeo.lua"/><file name="lua/playlist/vocaroo.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\vocaroo.lua"/><file name="lua/playlist/youtube.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\youtube.lua"/><file name="lua/playlist/zapiks.lua" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5803367051492f7809e523b21d76b7d\transformed\jetified-libvlc-all-3.6.0-eap14\assets\lua\playlist\zapiks.lua"/></source></dataSet><dataSet config=":video_player_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\video_player_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\sqflite_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\package_info_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":wakelock_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\wakelock_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\shared_preferences_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_vlc_player" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\flutter_vlc_player\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>