import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/user_profile.dart';
import '../services/local_database_service.dart';
import '../widgets/directional_focus_widget.dart';

class AddProfileScreen extends StatefulWidget {
  const AddProfileScreen({Key? key}) : super(key: key);

  @override
  State<AddProfileScreen> createState() => _AddProfileScreenState();
}

class _AddProfileScreenState extends State<AddProfileScreen>
    with TickerProviderStateMixin {
  final TextEditingController _nameController = TextEditingController();
  final FocusNode _nameFocus = FocusNode();
  final FocusNode _kidsToggleFocus = FocusNode();
  final FocusNode _createButtonFocus = FocusNode();
  final FocusNode _cancelButtonFocus = FocusNode();
  final LocalDatabaseService _dbService = LocalDatabaseService();

  bool _isKidsProfile = false;
  bool _isLoading = false;
  String _selectedEmoji = '😊';

  // Available emoji avatars
  final List<String> _adultEmojis = ['😊', '😎', '😍', '😂', '😉', '🤩', '🤔', '🥳', '🤖', '👽'];
  final List<String> _kidsEmojis = ['😊', '⭐', '💖', '🌈', '🦄', '🐱', '🐶', '🎈'];

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));

    _slideController.forward();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _nameFocus.dispose();
    _kidsToggleFocus.dispose();
    _createButtonFocus.dispose();
    _cancelButtonFocus.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: SlideTransition(
        position: _slideAnimation,
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    return Center(
      child: SingleChildScrollView(
        child: Container(
          width: 700,
          padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header (full width)
              _buildHeader(),

              const SizedBox(height: 25),

              // Two-column layout
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left Column - Form (bigger)
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        // Name field
                        _buildNameField(),

                        const SizedBox(height: 20),

                        // Kids profile toggle
                        _buildKidsToggle(),

                        const SizedBox(height: 25),

                        // Action buttons
                        _buildActionButtons(),
                      ],
                    ),
                  ),

                  const SizedBox(width: 30),

                  // Right Column - Avatar Preview (smaller)
                  Expanded(
                    flex: 1,
                    child: _buildAvatarPreview(),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatarPreview() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF2D2D44).withOpacity(0.8),
            const Color(0xFF1A1A2E).withOpacity(0.9),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Text(
            'Preview',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          // Avatar preview
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _isKidsProfile
                    ? [const Color(0xFF4CAF50), const Color(0xFF66BB6A)]
                    : [const Color(0xFF2196F3), const Color(0xFF42A5F5)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFFF6B35),
                width: 2,
              ),
            ),
            child: Center(
              child: Text(
                _selectedEmoji,
                style: const TextStyle(
                  fontSize: 36,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),

          // Profile name
          Text(
            _nameController.text.isNotEmpty ? _nameController.text : 'Name',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          // Kids badge
          if (_isKidsProfile) ...[
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'KIDS',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Avatar selection
          _buildAvatarGrid(),
        ],
      ),
    );
  }

  Widget _buildAvatarGrid() {
    final availableEmojis = _isKidsProfile ? _kidsEmojis : _adultEmojis;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Avatar',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),

        Container(
          height: 80,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              childAspectRatio: 1,
              crossAxisSpacing: 4,
              mainAxisSpacing: 4,
            ),
            itemCount: availableEmojis.length,
            itemBuilder: (context, index) {
              final emoji = availableEmojis[index];
              final isSelected = emoji == _selectedEmoji;

              return DirectionalFocusWidget(
                borderRadius: BorderRadius.circular(6),
                onPressed: () {
                  setState(() {
                    _selectedEmoji = emoji;
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFFFF6B35)
                        : Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFFFF6B35)
                          : Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      emoji,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Row(
          children: [
            RawKeyboardListener(
              focusNode: FocusNode(),
              onKey: (RawKeyEvent event) {
                if (event is RawKeyDownEvent && _cancelButtonFocus.hasFocus) {
                  if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
                    _nameFocus.requestFocus();
                  }
                }
              },
              child: DirectionalFocusWidget(
                focusNode: _cancelButtonFocus,
                borderRadius: BorderRadius.circular(8),
                onPressed: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3D3D5C),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 20),
            const Expanded(
              child: Text(
                'Add New Profile',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Text(
          'Create a personalized profile for your viewing experience',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14,
          ),
        ),
      ],
    );
  }



  Widget _buildNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profile Name',
          style: TextStyle(
            color: Colors.white.withOpacity(0.9),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        RawKeyboardListener(
          focusNode: FocusNode(),
          onKey: (RawKeyEvent event) {
            if (event is RawKeyDownEvent && _nameFocus.hasFocus) {
              if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
                _kidsToggleFocus.requestFocus();
              } else if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
                _cancelButtonFocus.requestFocus();
              }
            }
          },
          child: TextField(
            controller: _nameController,
            focusNode: _nameFocus,
            autofocus: true,
            style: const TextStyle(color: Colors.white, fontSize: 16),
            textInputAction: TextInputAction.next,
            onSubmitted: (value) {
              _kidsToggleFocus.requestFocus();
            },
            onChanged: (value) {
              setState(() {}); // Refresh avatar preview
            },
            decoration: InputDecoration(
              hintText: 'Enter profile name',
              hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
              filled: true,
              fillColor: const Color(0xFF3D3D5C),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFFF6B35), width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              prefixIcon: const Icon(Icons.person, color: Color(0xFFFF6B35)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildKidsToggle() {
    return RawKeyboardListener(
      focusNode: FocusNode(),
      onKey: (RawKeyEvent event) {
        if (event is RawKeyDownEvent && _kidsToggleFocus.hasFocus) {
          if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
            _nameFocus.requestFocus();
          } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
            _createButtonFocus.requestFocus();
          }
        }
      },
      child: DirectionalFocusWidget(
        focusNode: _kidsToggleFocus,
        borderRadius: BorderRadius.circular(12),
        onPressed: () {
          setState(() {
            _isKidsProfile = !_isKidsProfile;
            // Reset to first emoji of the new category
            _selectedEmoji = _isKidsProfile ? _kidsEmojis.first : _adultEmojis.first;
          });
        },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF3D3D5C),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.child_care,
              color: _isKidsProfile ? const Color(0xFF4CAF50) : Colors.white.withOpacity(0.7),
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Kids Profile',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    'Safe content for children',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: 50,
              height: 28,
              decoration: BoxDecoration(
                color: _isKidsProfile ? const Color(0xFF4CAF50) : Colors.grey,
                borderRadius: BorderRadius.circular(14),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                alignment: _isKidsProfile ? Alignment.centerRight : Alignment.centerLeft,
                child: Container(
                  width: 24,
                  height: 24,
                  margin: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return RawKeyboardListener(
      focusNode: FocusNode(),
      onKey: (RawKeyEvent event) {
        if (event is RawKeyDownEvent && _createButtonFocus.hasFocus) {
          if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
            _kidsToggleFocus.requestFocus();
          }
        }
      },
      child: SizedBox(
        width: double.infinity,
        child: DirectionalFocusWidget(
          focusNode: _createButtonFocus,
          borderRadius: BorderRadius.circular(12),
          onPressed: _isLoading ? null : _createProfile,
        child: Container(
          height: 50,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFFB347), Color(0xFFFF6B35)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Text(
                    'Create Profile',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ),
      ),
    );
  }

  void _createProfile() async {
    final name = _nameController.text.trim();
    
    if (name.isEmpty) {
      _showSnackBar('Please enter a profile name', isError: true);
      return;
    }

    if (name.length < 2) {
      _showSnackBar('Profile name must be at least 2 characters', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create new profile
      final profile = UserProfile(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        avatarPath: _selectedEmoji, // Store the selected emoji as avatar
        isKidsProfile: _isKidsProfile,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      // Save to database
      final success = await _dbService.saveProfile(profile);
      
      if (success && mounted) {
        // Profile created successfully - no need for message, just navigate
        
        // Return to profile selection
        Navigator.pop(context, true);
      } else {
        _showSnackBar('Failed to create profile', isError: true);
      }
    } catch (e) {
      _showSnackBar('Error creating profile: ${e.toString()}', isError: true);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
