{"logs": [{"outputFile": "com.example.iptv_player.app-mergeDebugResources-44:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46567eeb5dd96eb1eece9970999eba2d\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "111,258,259,260,261,1960,1962,1963,1968,1970", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "5491,15573,15626,15679,15732,125647,125823,125945,126207,126402", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "5575,15621,15674,15727,15780,125708,125940,126001,126268,126464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e8d563d70ff69cea851b997b886ecf6b\\transformed\\jetified-appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2244,2260,2266,3591,3607", "startColumns": "4,4,4,4,4", "startOffsets": "142441,142866,143044,189404,189815", "endLines": "2259,2265,2275,3606,3610", "endColumns": "24,24,24,24,24", "endOffsets": "142861,143039,143323,189810,189937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,85,86,109,110,214,215,216,217,218,219,220,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,315,316,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,361,392,393,394,395,396,397,398,415,1955,1956,1961,1964,1969,2114,2115,2780,2825,2995,3028,3058,3091", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2715,2787,3875,3940,5359,5428,12504,12574,12642,12714,12784,12845,12919,14162,14223,14284,14346,14410,14472,14533,14601,14701,14761,14827,14900,14969,15026,15078,16238,16310,16386,16451,16510,16569,16629,16689,16749,16809,16869,16929,16989,17049,17109,17169,17228,17288,17348,17408,17468,17528,17588,17648,17708,17768,17828,17887,17947,18007,18066,18125,18184,18243,18302,18870,18905,19491,19546,19609,19664,19722,19780,19841,19904,19961,20012,20062,20123,20180,20246,20280,20315,21288,23465,23532,23604,23673,23742,23816,23888,24954,125329,125446,125713,126006,126273,137764,137836,159715,161689,169524,171255,172255,172937", "endLines": "29,70,71,85,86,109,110,214,215,216,217,218,219,220,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,315,316,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,361,392,393,394,395,396,397,398,415,1955,1959,1961,1967,1969,2114,2115,2785,2834,3027,3048,3090,3096", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2782,2870,3935,4001,5423,5486,12569,12637,12709,12779,12840,12914,12987,14218,14279,14341,14405,14467,14528,14596,14696,14756,14822,14895,14964,15021,15073,15135,16305,16381,16446,16505,16564,16624,16684,16744,16804,16864,16924,16984,17044,17104,17164,17223,17283,17343,17403,17463,17523,17583,17643,17703,17763,17823,17882,17942,18002,18061,18120,18179,18238,18297,18356,18900,18935,19541,19604,19659,19717,19775,19836,19899,19956,20007,20057,20118,20175,20241,20275,20310,20345,21353,23527,23599,23668,23737,23811,23883,23971,25020,125441,125642,125818,126202,126397,137831,137898,159913,161985,171250,171931,172932,173099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45af1ebc35cbf9d2d2886a132166b73a\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "311,312,317,324,325,344,345,346,347,348", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "18683,18723,18940,19278,19333,20350,20404,20456,20505,20566", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "18718,18765,18978,19328,19375,20399,20451,20500,20561,20611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e0d9d3675465ff69d847e2f781f20c61\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "389", "startColumns": "4", "startOffsets": "23224", "endColumns": "82", "endOffsets": "23302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b3d51a44ab6b56289d4858158a1ad6dd\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "352", "startColumns": "4", "startOffsets": "20751", "endColumns": "53", "endOffsets": "20800"}}, {"source": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,15", "startColumns": "4,4", "startOffsets": "176,887", "endLines": "8,18", "endColumns": "12,12", "endOffsets": "538,1097"}, "to": {"startLines": "1524,1529", "startColumns": "4,4", "startOffsets": "96140,96375", "endLines": "1528,1532", "endColumns": "12,12", "endOffsets": "96370,96582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5093ab42d2307deb2d7ac0b7f5718c38\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,309,2223,2229,3552,3560,3575", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,18566,141571,141766,187854,188136,188750", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,309,2228,2233,3559,3574,3590", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,18621,141761,141919,188131,188745,189399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\55520e4df2220e27f13f0bbb7467d11a\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "310,326,354,3049,3054", "startColumns": "4,4,4,4,4", "startOffsets": "18626,19380,20855,171936,172106", "endLines": "310,326,354,3053,3057", "endColumns": "56,64,63,24,24", "endOffsets": "18678,19440,20914,172101,172250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75e1bcd7a8b61b1e132d50e7766bfd37\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "223,224,225,233,234,235,314,3472", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13165,13224,13272,13939,14014,14090,18804,185212", "endLines": "223,224,225,233,234,235,314,3491", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "13219,13267,13323,14009,14085,14157,18865,186002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "62,112,251,252,253,254,255,256,257,318,319,320,359,360,399,409,410,411,416,417,418,1507,1693,1696,1702,1708,1711,1717,1721,1724,1731,1737,1740,1746,1751,1756,1763,1765,1771,1777,1785,1790,1797,1802,1808,1812,1819,1823,1829,1835,1838,1842,1843,2771,2786,2953,2991,3133,3308,3326,3390,3400,3410,3417,3423,3527,3677,3694", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2141,5580,15140,15204,15259,15327,15394,15459,15516,18983,19031,19079,21162,21225,23976,24660,24717,24761,25025,25164,25214,94702,108504,108609,108854,109192,109338,109678,109890,110053,110460,110798,110921,111260,111499,111756,112127,112187,112525,112811,113260,113552,113940,114245,114589,114834,115164,115371,115639,115912,116056,116257,116304,159395,159918,168074,169375,174317,179912,180540,182465,182747,183052,183314,183574,187090,192968,193498", "endLines": "62,112,251,252,253,254,255,256,257,318,319,320,359,360,399,409,410,413,416,417,418,1523,1695,1701,1707,1710,1716,1720,1723,1730,1736,1739,1745,1750,1755,1762,1764,1770,1776,1784,1789,1796,1801,1807,1811,1818,1822,1828,1834,1837,1841,1842,1843,2775,2796,2972,2994,3142,3315,3389,3399,3409,3416,3422,3465,3539,3693,3710", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2209,5644,15199,15254,15322,15389,15454,15511,15568,19026,19074,19135,21220,21283,24009,24712,24756,24896,25159,25209,25257,96135,108604,108849,109187,109333,109673,109885,110048,110455,110793,110916,111255,111494,111751,112122,112182,112520,112806,113255,113547,113935,114240,114584,114829,115159,115366,115634,115907,116051,116252,116299,116355,159575,160314,168798,169519,174644,180155,182460,182742,183047,183309,183569,184992,187537,193493,194061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e5803367051492f7809e523b21d76b7d\\transformed\\jetified-libvlc-all-3.6.0-eap14\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,95,174", "endColumns": "39,78,78", "endOffsets": "90,169,248"}, "to": {"startLines": "76,390,391", "startColumns": "4,4,4", "startOffsets": "3194,23307,23386", "endColumns": "39,78,78", "endOffsets": "3229,23381,23460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b91be3af319ede480d7185430690ee1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "353", "startColumns": "4", "startOffsets": "20805", "endColumns": "49", "endOffsets": "20850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13dd610fda78ecd8ad3daad9b8195d7e\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "323,351", "startColumns": "4,4", "startOffsets": "19236,20691", "endColumns": "41,59", "endOffsets": "19273,20746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c092edbccc16347970ed4f22e8da111a\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "350", "startColumns": "4", "startOffsets": "20648", "endColumns": "42", "endOffsets": "20686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7af0f8379b1dddc34dee76692b56b3d0\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,77,78,79,80,81,82,83,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,227,228,229,230,231,232,262,263,264,265,266,267,268,269,305,306,307,308,313,321,322,327,349,355,356,357,358,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,414,419,420,421,422,423,424,432,433,437,441,445,450,456,463,467,471,476,480,484,488,492,496,500,506,510,516,520,526,530,535,539,542,546,552,556,562,566,572,575,579,583,587,591,595,596,597,598,601,604,607,610,614,615,616,617,618,621,623,625,627,632,633,637,643,647,648,650,662,663,667,673,677,678,679,683,710,714,715,719,747,919,945,1116,1142,1173,1181,1187,1203,1225,1230,1235,1245,1254,1263,1267,1274,1293,1300,1301,1310,1313,1316,1320,1324,1328,1331,1332,1337,1342,1352,1357,1364,1370,1371,1374,1378,1383,1385,1387,1390,1393,1395,1399,1402,1409,1412,1415,1419,1421,1425,1427,1429,1431,1435,1443,1451,1463,1469,1478,1481,1492,1495,1496,1501,1502,1533,1602,1672,1673,1683,1692,1844,1846,1850,1853,1856,1859,1862,1865,1868,1871,1875,1878,1881,1884,1888,1891,1895,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1932,1933,1935,1936,1938,1940,1941,1943,1944,1945,1946,1947,1948,1950,1951,1952,1953,1954,1971,1973,1975,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1991,1992,1993,1994,1995,1996,1997,1999,2003,2007,2008,2009,2010,2011,2012,2016,2017,2018,2019,2021,2023,2025,2027,2029,2030,2031,2032,2034,2036,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2052,2053,2054,2055,2057,2059,2060,2062,2063,2065,2067,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2082,2083,2084,2085,2087,2088,2089,2090,2091,2093,2095,2097,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2119,2194,2197,2200,2203,2217,2234,2276,2279,2308,2335,2344,2408,2776,2797,2835,2973,3097,3121,3127,3143,3164,3288,3316,3322,3466,3492,3540,3611,3711,3731,3786,3798,3824", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2214,2278,2348,2409,2484,2560,2637,2875,2960,3042,3118,3234,3311,3389,3495,3601,3680,3760,3817,4006,4080,4155,4220,4286,4346,4407,4479,4552,4619,4687,4746,4805,4864,4923,4982,5036,5090,5143,5197,5251,5305,5649,5723,5802,5875,5949,6020,6092,6164,6237,6294,6352,6425,6499,6573,6648,6720,6793,6863,6934,6994,7055,7124,7193,7263,7337,7413,7477,7554,7630,7707,7772,7841,7918,7993,8062,8130,8207,8273,8334,8431,8496,8565,8664,8735,8794,8852,8909,8968,9032,9103,9175,9247,9319,9391,9458,9526,9594,9653,9716,9780,9870,9961,10021,10087,10154,10220,10290,10354,10407,10474,10535,10602,10715,10773,10836,10901,10966,11041,11114,11186,11230,11277,11323,11372,11433,11494,11555,11617,11681,11745,11809,11874,11937,11997,12058,12124,12183,12243,12305,12376,12436,12992,13078,13328,13418,13505,13593,13675,13758,13848,15785,15837,15895,15940,16006,16070,16127,16184,18361,18418,18466,18515,18770,19140,19187,19445,20616,20919,20983,21045,21105,21358,21432,21502,21580,21634,21704,21789,21837,21883,21944,22007,22073,22137,22208,22271,22336,22400,22461,22522,22574,22647,22721,22790,22865,22939,23013,23154,24901,25262,25340,25430,25518,25614,25704,26286,26375,26622,26903,27155,27440,27833,28310,28532,28754,29030,29257,29487,29717,29947,30177,30404,30823,31049,31474,31704,32132,32351,32634,32842,32973,33200,33626,33851,34278,34499,34924,35044,35320,35621,35945,36236,36550,36687,36818,36923,37165,37332,37536,37744,38015,38127,38239,38344,38461,38675,38821,38961,39047,39395,39483,39729,40147,40396,40478,40576,41233,41333,41585,42009,42264,42358,42447,42684,44708,44950,45052,45305,47461,58142,59658,70353,71881,73638,74264,74684,75945,77210,77466,77702,78249,78743,79348,79546,80126,81494,81869,81987,82525,82682,82878,83151,83407,83577,83718,83782,84147,84514,85190,85454,85792,86145,86239,86425,86731,86993,87118,87245,87484,87695,87814,88007,88184,88639,88820,88942,89201,89314,89501,89603,89710,89839,90114,90622,91118,91995,92289,92859,93008,93740,93912,93996,94332,94424,96587,101818,107189,107251,107829,108413,116360,116473,116702,116862,117014,117185,117351,117520,117687,117850,118093,118263,118436,118607,118881,119080,119285,119615,119699,119795,119891,119989,120089,120191,120293,120395,120497,120599,120699,120795,120907,121036,121159,121290,121421,121519,121633,121727,121867,122001,122097,122209,122309,122425,122521,122633,122733,122873,123009,123173,123303,123461,123611,123752,123896,124031,124143,124293,124421,124549,124685,124817,124947,125077,125189,126469,126615,126759,126897,126963,127053,127129,127233,127323,127425,127533,127641,127741,127821,127913,128011,128121,128173,128251,128357,128449,128553,128663,128785,128948,129105,129185,129285,129375,129485,129575,129816,129910,130016,130108,130208,130320,130434,130550,130666,130760,130874,130986,131088,131208,131330,131412,131516,131636,131762,131860,131954,132042,132154,132270,132392,132504,132679,132795,132881,132973,133085,133209,133276,133402,133470,133598,133742,133870,133939,134034,134149,134262,134361,134470,134581,134692,134793,134898,134998,135128,135219,135342,135436,135548,135634,135738,135834,135922,136040,136144,136248,136374,136462,136570,136670,136760,136870,136954,137056,137140,137194,137258,137364,137450,137560,137644,138048,140664,140782,140897,140977,141338,141924,143328,143406,144750,146111,146499,149342,159580,160319,161990,168803,173104,173855,174117,174649,175028,179306,180160,180389,184997,186007,187542,189942,194066,194810,196941,197281,198592", "endLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,77,78,79,80,81,82,83,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,227,228,229,230,231,232,262,263,264,265,266,267,268,269,305,306,307,308,313,321,322,327,349,355,356,357,358,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,414,419,420,421,422,423,431,432,436,440,444,449,455,462,466,470,475,479,483,487,491,495,499,505,509,515,519,525,529,534,538,541,545,551,555,561,565,571,574,578,582,586,590,594,595,596,597,600,603,606,609,613,614,615,616,617,620,622,624,626,631,632,636,642,646,647,649,661,662,666,672,676,677,678,682,709,713,714,718,746,918,944,1115,1141,1172,1180,1186,1202,1224,1229,1234,1244,1253,1262,1266,1273,1292,1299,1300,1309,1312,1315,1319,1323,1327,1330,1331,1336,1341,1351,1356,1363,1369,1370,1373,1377,1382,1384,1386,1389,1392,1394,1398,1401,1408,1411,1414,1418,1420,1424,1426,1428,1430,1434,1442,1450,1462,1468,1477,1480,1491,1494,1495,1500,1501,1506,1601,1671,1672,1682,1691,1692,1845,1849,1852,1855,1858,1861,1864,1867,1870,1874,1877,1880,1883,1887,1890,1894,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1920,1922,1923,1924,1925,1926,1927,1928,1929,1931,1932,1934,1935,1937,1939,1940,1942,1943,1944,1945,1946,1947,1949,1950,1951,1952,1953,1954,1972,1974,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1990,1991,1992,1993,1994,1995,1996,1998,2002,2006,2007,2008,2009,2010,2011,2015,2016,2017,2018,2020,2022,2024,2026,2028,2029,2030,2031,2033,2035,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2051,2052,2053,2054,2056,2058,2059,2061,2062,2064,2066,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2081,2082,2083,2084,2086,2087,2088,2089,2090,2092,2094,2096,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2193,2196,2199,2202,2216,2222,2243,2278,2307,2334,2343,2407,2770,2779,2824,2862,2990,3120,3126,3132,3163,3287,3307,3321,3325,3471,3526,3551,3676,3730,3785,3797,3823,3830", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2136,2273,2343,2404,2479,2555,2632,2710,2955,3037,3113,3189,3306,3384,3490,3596,3675,3755,3812,3870,4075,4150,4215,4281,4341,4402,4474,4547,4614,4682,4741,4800,4859,4918,4977,5031,5085,5138,5192,5246,5300,5354,5718,5797,5870,5944,6015,6087,6159,6232,6289,6347,6420,6494,6568,6643,6715,6788,6858,6929,6989,7050,7119,7188,7258,7332,7408,7472,7549,7625,7702,7767,7836,7913,7988,8057,8125,8202,8268,8329,8426,8491,8560,8659,8730,8789,8847,8904,8963,9027,9098,9170,9242,9314,9386,9453,9521,9589,9648,9711,9775,9865,9956,10016,10082,10149,10215,10285,10349,10402,10469,10530,10597,10710,10768,10831,10896,10961,11036,11109,11181,11225,11272,11318,11367,11428,11489,11550,11612,11676,11740,11804,11869,11932,11992,12053,12119,12178,12238,12300,12371,12431,12499,13073,13160,13413,13500,13588,13670,13753,13843,13934,15832,15890,15935,16001,16065,16122,16179,16233,18413,18461,18510,18561,18799,19182,19231,19486,20643,20978,21040,21100,21157,21427,21497,21575,21629,21699,21784,21832,21878,21939,22002,22068,22132,22203,22266,22331,22395,22456,22517,22569,22642,22716,22785,22860,22934,23008,23149,23219,24949,25335,25425,25513,25609,25699,26281,26370,26617,26898,27150,27435,27828,28305,28527,28749,29025,29252,29482,29712,29942,30172,30399,30818,31044,31469,31699,32127,32346,32629,32837,32968,33195,33621,33846,34273,34494,34919,35039,35315,35616,35940,36231,36545,36682,36813,36918,37160,37327,37531,37739,38010,38122,38234,38339,38456,38670,38816,38956,39042,39390,39478,39724,40142,40391,40473,40571,41228,41328,41580,42004,42259,42353,42442,42679,44703,44945,45047,45300,47456,58137,59653,70348,71876,73633,74259,74679,75940,77205,77461,77697,78244,78738,79343,79541,80121,81489,81864,81982,82520,82677,82873,83146,83402,83572,83713,83777,84142,84509,85185,85449,85787,86140,86234,86420,86726,86988,87113,87240,87479,87690,87809,88002,88179,88634,88815,88937,89196,89309,89496,89598,89705,89834,90109,90617,91113,91990,92284,92854,93003,93735,93907,93991,94327,94419,94697,101813,107184,107246,107824,108408,108499,116468,116697,116857,117009,117180,117346,117515,117682,117845,118088,118258,118431,118602,118876,119075,119280,119610,119694,119790,119886,119984,120084,120186,120288,120390,120492,120594,120694,120790,120902,121031,121154,121285,121416,121514,121628,121722,121862,121996,122092,122204,122304,122420,122516,122628,122728,122868,123004,123168,123298,123456,123606,123747,123891,124026,124138,124288,124416,124544,124680,124812,124942,125072,125184,125324,126610,126754,126892,126958,127048,127124,127228,127318,127420,127528,127636,127736,127816,127908,128006,128116,128168,128246,128352,128444,128548,128658,128780,128943,129100,129180,129280,129370,129480,129570,129811,129905,130011,130103,130203,130315,130429,130545,130661,130755,130869,130981,131083,131203,131325,131407,131511,131631,131757,131855,131949,132037,132149,132265,132387,132499,132674,132790,132876,132968,133080,133204,133271,133397,133465,133593,133737,133865,133934,134029,134144,134257,134356,134465,134576,134687,134788,134893,134993,135123,135214,135337,135431,135543,135629,135733,135829,135917,136035,136139,136243,136369,136457,136565,136665,136755,136865,136949,137051,137135,137189,137253,137359,137445,137555,137639,137759,140659,140777,140892,140972,141333,141566,142436,143401,144745,146106,146494,149337,159390,159710,161684,163342,169370,173850,174112,174312,175023,179301,179907,180384,180535,185207,187085,187849,192963,194805,196936,197276,198587,198790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "400,401,402,403,404,405,406,407,408", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "24014,24084,24146,24211,24275,24352,24417,24507,24591", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "24079,24141,24206,24270,24347,24412,24502,24586,24655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\58a0920e123e93dd6aa702d27ab7530e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2116,2863,2869", "startColumns": "4,4,4,4", "startOffsets": "164,137903,163347,163558", "endLines": "3,2118,2868,2952", "endColumns": "60,12,24,24", "endOffsets": "220,138043,163553,168069"}}]}]}