import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'theme/app_theme.dart';
import 'screens/app_initializer.dart';
import 'services/local_database_service.dart';
import 'models/user_profile.dart';

void main() {
  runApp(const IPTVPlayerApp());
  _setupMethodChannels();
}

void _setupMethodChannels() {
  const platform = MethodChannel('com.example.iptv_player/continue_watching');

  platform.setMethodCallHandler((call) async {
    switch (call.method) {
      case 'saveContinueWatching':
        await _saveContinueWatching(call.arguments);
        break;
      default:
        throw PlatformException(
          code: 'Unimplemented',
          details: 'Method ${call.method} not implemented',
        );
    }
  });
}

Future<void> _saveContinueWatching(Map<dynamic, dynamic> data) async {
  try {
    final databaseService = LocalDatabaseService();

    // Get current user profile
    final userProfile = await databaseService.getCurrentProfile();
    if (userProfile == null) return;

    // Create continue watching item
    final continueWatchingItem = ContinueWatchingItem(
      id: data['movie_id'] as String,
      title: data['movie_title'] as String,
      type: 'movie',
      streamId: data['movie_id'] as String,
      thumbnailUrl: data['movie_thumbnail'] as String,
      watchedDuration: (data['current_position'] as int) ~/ 1000, // Convert milliseconds to seconds
      totalDuration: (data['total_duration'] as int) ~/ 1000, // Convert milliseconds to seconds
      lastWatched: DateTime.fromMillisecondsSinceEpoch(data['last_watched'] as int),
    );

    // Add to continue watching list
    final success = await databaseService.addToContinueWatching(userProfile.id.toString(), continueWatchingItem);

    final progressPercent = continueWatchingItem.totalDuration > 0
        ? (continueWatchingItem.watchedDuration / continueWatchingItem.totalDuration * 100).round()
        : 0;

    if (success) {
      print('✅ Continue watching saved: ${continueWatchingItem.title} - $progressPercent%');
    } else {
      print('❌ Failed to save continue watching: ${continueWatchingItem.title}');
    }
  } catch (e) {
    print('Error saving continue watching: $e');
  }
}



class IPTVPlayerApp extends StatelessWidget {
  const IPTVPlayerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'IPTV Player',
      theme: AppTheme.darkTheme,
      home: const AppInitializer(),
      debugShowCheckedModeBanner: false,
      shortcuts: {
        // TV remote control shortcuts
        LogicalKeySet(LogicalKeyboardKey.select): const ActivateIntent(),
        LogicalKeySet(LogicalKeyboardKey.enter): const ActivateIntent(),
        LogicalKeySet(LogicalKeyboardKey.space): const ActivateIntent(),
      },
    );
  }
}


