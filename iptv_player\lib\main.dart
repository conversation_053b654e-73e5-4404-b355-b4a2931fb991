import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'theme/app_theme.dart';
import 'screens/app_initializer.dart';

void main() {
  runApp(const IPTVPlayerApp());
}

class IPTVPlayerApp extends StatelessWidget {
  const IPTVPlayerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'IPTV Player',
      theme: AppTheme.darkTheme,
      home: const AppInitializer(),
      debugShowCheckedModeBanner: false,
      shortcuts: {
        // TV remote control shortcuts
        LogicalKeySet(LogicalKeyboardKey.select): const ActivateIntent(),
        LogicalKeySet(LogicalKeyboardKey.enter): const ActivateIntent(),
        LogicalKeySet(LogicalKeyboardKey.space): const ActivateIntent(),
      },
    );
  }
}


