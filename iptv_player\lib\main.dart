import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'theme/app_theme.dart';
import 'screens/app_initializer.dart';
import 'services/database_service.dart';
import 'models/user_profile.dart';

void main() {
  runApp(const IPTVPlayerApp());
  _setupMethodChannels();
}

void _setupMethodChannels() {
  const platform = MethodChannel('com.example.iptv_player/continue_watching');

  platform.setMethodCallHandler((call) async {
    switch (call.method) {
      case 'saveContinueWatching':
        await _saveContinueWatching(call.arguments);
        break;
      default:
        throw PlatformException(
          code: 'Unimplemented',
          details: 'Method ${call.method} not implemented',
        );
    }
  });
}

Future<void> _saveContinueWatching(Map<dynamic, dynamic> data) async {
  try {
    final databaseService = DatabaseService();

    // Get current user profile
    final userProfile = await databaseService.getUserProfile();
    if (userProfile == null) return;

    // Create continue watching item
    final continueWatchingItem = ContinueWatchingItem(
      movieId: data['movie_id'] as String,
      movieTitle: data['movie_title'] as String,
      movieThumbnail: data['movie_thumbnail'] as String,
      currentPosition: data['current_position'] as int,
      totalDuration: data['total_duration'] as int,
      progressPercent: data['progress_percent'] as int,
      lastWatched: DateTime.fromMillisecondsSinceEpoch(data['last_watched'] as int),
    );

    // Add to continue watching list
    await databaseService.addToContinueWatching(userProfile.id, continueWatchingItem);

    print('Continue watching saved: ${continueWatchingItem.movieTitle} - ${continueWatchingItem.progressPercent}%');
  } catch (e) {
    print('Error saving continue watching: $e');
  }
}

class IPTVPlayerApp extends StatelessWidget {
  const IPTVPlayerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'IPTV Player',
      theme: AppTheme.darkTheme,
      home: const AppInitializer(),
      debugShowCheckedModeBanner: false,
      shortcuts: {
        // TV remote control shortcuts
        LogicalKeySet(LogicalKeyboardKey.select): const ActivateIntent(),
        LogicalKeySet(LogicalKeyboardKey.enter): const ActivateIntent(),
        LogicalKeySet(LogicalKeyboardKey.space): const ActivateIntent(),
      },
    );
  }
}


