[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-xxxhdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-xxxhdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\drawable_launch_logo_vector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\drawable\\launch_logo_vector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\drawable_splash_screen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\drawable\\splash_screen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-hdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-hdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\drawable_loading_dots.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\drawable\\loading_dots.xml"}, {"merged": "com.example.iptv_player.app-debug-42:/xml_network_security_config.xml.flat", "source": "com.example.iptv_player.app-main-37:/xml/network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\drawable_logo_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\drawable\\logo_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-xxhdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-xxhdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-xhdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-xhdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\drawable_launch_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\drawable\\launch_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\drawable_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\drawable\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-mdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-mdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\drawable-v21_launch_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\drawable-v21\\launch_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-42:\\drawable_logo_small.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-37:\\drawable\\logo_small.xml"}]