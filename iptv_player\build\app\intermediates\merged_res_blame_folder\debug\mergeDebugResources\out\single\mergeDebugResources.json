[{"merged": "com.example.iptv_player.app-debug-45:/layout_custom_player_control_view.xml.flat", "source": "com.example.iptv_player.app-main-40:/layout/custom_player_control_view.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_skip_previous.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_skip_previous.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_launch_logo_vector.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/launch_logo_vector.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/color_button_tint_selector.xml.flat", "source": "com.example.iptv_player.app-main-40:/color/button_tint_selector.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_audiotrack.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_audiotrack.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_replay_10.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_replay_10.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_gradient_top.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/gradient_top.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_logo_small.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/logo_small.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_logo.png.flat", "source": "com.example.iptv_player.app-main-40:/drawable/logo.png"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_settings.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_settings.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_button_selector.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/button_selector.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_launch_logo.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/launch_logo.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-xxxhdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-xxxhdpi/logo.png"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_fullscreen.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_fullscreen.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_arrow_back.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_arrow_back.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_forward_10.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_forward_10.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_splash_screen.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/splash_screen.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/layout_activity_exoplayer.xml.flat", "source": "com.example.iptv_player.app-main-40:/layout/activity_exoplayer.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_logo_splash.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/logo_splash.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-hdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-hdpi/logo.png"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-xxhdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-xxhdpi/logo.png"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_skip_next.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_skip_next.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_loading_dots.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/loading_dots.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_hd.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_hd.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable-v21_launch_background.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable-v21/launch_background.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_subtitles.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_subtitles.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-mdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-mdpi/logo.png"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_gradient_bottom.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/gradient_bottom.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/xml_network_security_config.xml.flat", "source": "com.example.iptv_player.app-main-40:/xml/network_security_config.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/drawable_ic_volume_up.xml.flat", "source": "com.example.iptv_player.app-main-40:/drawable/ic_volume_up.xml"}, {"merged": "com.example.iptv_player.app-debug-45:/mipmap-xhdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-40:/mipmap-xhdpi/logo.png"}]