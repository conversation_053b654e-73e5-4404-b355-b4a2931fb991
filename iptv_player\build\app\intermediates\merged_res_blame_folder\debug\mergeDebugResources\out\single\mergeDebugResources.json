[{"merged": "com.example.iptv_player.app-debug-46:/mipmap-mdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-mdpi/logo.png"}, {"merged": "com.example.iptv_player.app-debug-46:/drawable_launch_logo_vector.xml.flat", "source": "com.example.iptv_player.app-main-41:/drawable/launch_logo_vector.xml"}, {"merged": "com.example.iptv_player.app-debug-46:/drawable-v21_launch_background.xml.flat", "source": "com.example.iptv_player.app-main-41:/drawable-v21/launch_background.xml"}, {"merged": "com.example.iptv_player.app-debug-46:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-46:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-46:/mipmap-xxxhdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-xxxhdpi/logo.png"}, {"merged": "com.example.iptv_player.app-debug-46:/drawable_splash_screen.xml.flat", "source": "com.example.iptv_player.app-main-41:/drawable/splash_screen.xml"}, {"merged": "com.example.iptv_player.app-debug-46:/mipmap-xhdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-xhdpi/logo.png"}, {"merged": "com.example.iptv_player.app-debug-46:/drawable_loading_dots.xml.flat", "source": "com.example.iptv_player.app-main-41:/drawable/loading_dots.xml"}, {"merged": "com.example.iptv_player.app-debug-46:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-46:/mipmap-hdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-hdpi/logo.png"}, {"merged": "com.example.iptv_player.app-debug-46:/drawable_logo_splash.xml.flat", "source": "com.example.iptv_player.app-main-41:/drawable/logo_splash.xml"}, {"merged": "com.example.iptv_player.app-debug-46:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-46:/xml_network_security_config.xml.flat", "source": "com.example.iptv_player.app-main-41:/xml/network_security_config.xml"}, {"merged": "com.example.iptv_player.app-debug-46:/mipmap-xxhdpi_logo.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-xxhdpi/logo.png"}, {"merged": "com.example.iptv_player.app-debug-46:/drawable_launch_logo.xml.flat", "source": "com.example.iptv_player.app-main-41:/drawable/launch_logo.xml"}, {"merged": "com.example.iptv_player.app-debug-46:/drawable_logo.png.flat", "source": "com.example.iptv_player.app-main-41:/drawable/logo.png"}, {"merged": "com.example.iptv_player.app-debug-46:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.example.iptv_player.app-main-41:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-46:/drawable_logo_small.xml.flat", "source": "com.example.iptv_player.app-main-41:/drawable/logo_small.xml"}]