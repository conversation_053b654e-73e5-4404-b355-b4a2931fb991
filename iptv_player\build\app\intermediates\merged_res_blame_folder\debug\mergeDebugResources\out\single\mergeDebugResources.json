[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "com.example.iptv_player.app-debug-45:/layout_custom_player_control_view.xml.flat", "source": "com.example.iptv_player.app-main-40:/layout/custom_player_control_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_forward_10.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_forward_10.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_skip_previous.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_skip_previous.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_skip_next.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_skip_next.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_loading_dots.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\loading_dots.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\color_button_tint_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\color\\button_tint_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_audiotrack.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_audiotrack.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_replay_10.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_replay_10.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_fullscreen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_fullscreen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-xxxhdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-xxxhdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-xxhdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-xxhdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_hd.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_hd.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-mdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-mdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_gradient_bottom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\gradient_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-xhdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-xhdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_launch_logo_vector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\launch_logo_vector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_logo_small.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\logo_small.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_launch_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\launch_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_gradient_top.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\gradient_top.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\layout_activity_exoplayer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\layout\\activity_exoplayer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_button_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\button_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable-v21_launch_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable-v21\\launch_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_subtitles.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_subtitles.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\layout_custom_player_control_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\layout\\custom_player_control_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_logo_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\logo_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_ic_volume_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\ic_volume_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\mipmap-hdpi_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\mipmap-hdpi\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-debug-45:\\drawable_splash_screen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.iptv_player.app-main-40:\\drawable\\splash_screen.xml"}]