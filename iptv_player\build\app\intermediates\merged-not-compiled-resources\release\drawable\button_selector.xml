<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Focused state -->
    <item android:state_focused="true">
        <shape android:shape="oval">
            <solid android:color="#FFE17055" />
        </shape>
    </item>
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#FFCC5A44" />
        </shape>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
</selector>
