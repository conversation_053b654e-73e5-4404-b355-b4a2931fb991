{"logs": [{"outputFile": "com.example.iptv_player.app-mergeDebugResources-43:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6360,6431,6495,6559,6626,6703,6772,6861,6944", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "6426,6490,6554,6621,6698,6767,6856,6939,7011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "53,105,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4188,8066,8467,8548,8877,9046,9126", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "4255,8151,8543,8689,9041,9121,9198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "54,106,107,108", "startColumns": "4,4,4,4", "startOffsets": "4260,8156,8257,8368", "endColumns": "102,100,110,98", "endOffsets": "4358,8252,8363,8462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,521,629,749,827,904,996,1089,1184,1278,1379,1473,1569,1664,1756,1848,1929,2040,2143,2242,2357,2471,2574,2729,2832", "endColumns": "117,104,106,85,107,119,77,76,91,92,94,93,100,93,95,94,91,91,80,110,102,98,114,113,102,154,102,81", "endOffsets": "218,323,430,516,624,744,822,899,991,1084,1179,1273,1374,1468,1564,1659,1751,1843,1924,2035,2138,2237,2352,2466,2569,2724,2827,2909"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "727,845,950,1057,1143,1251,1371,1449,1526,1618,1711,1806,1900,2001,2095,2191,2286,2378,2470,2551,2662,2765,2864,2979,3093,3196,3351,8694", "endColumns": "117,104,106,85,107,119,77,76,91,92,94,93,100,93,95,94,91,91,80,110,102,98,114,113,102,154,102,81", "endOffsets": "840,945,1052,1138,1246,1366,1444,1521,1613,1706,1801,1895,1996,2090,2186,2281,2373,2465,2546,2657,2760,2859,2974,3088,3191,3346,3449,8771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3454,3556,3658,3758,3858,3965,4069,8776", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3551,3653,3753,3853,3960,4064,4183,8872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\058b46d59488c77d02cd8814b15f89a0\\transformed\\jetified-media3-ui-1.4.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1848,1967,2082,2155,2234,2309,2378,2461,2543,2609,2674,2727,2785,2833,2894,2959,3021,3086,3154,3212,3270,3336,3388,3450,3526,3602,3657", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,51,61,75,75,54,66", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1843,1962,2077,2150,2229,2304,2373,2456,2538,2604,2669,2722,2780,2828,2889,2954,3016,3081,3149,3207,3265,3331,3383,3445,3521,3597,3652,3719"}, "to": {"startLines": "2,11,15,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,536,4363,4452,4540,4620,4713,4806,4879,4946,5048,5146,5214,5281,5346,5415,5534,5653,5768,5841,5920,5995,6064,6147,6229,6295,7016,7069,7127,7175,7236,7301,7363,7428,7496,7554,7612,7678,7730,7792,7868,7944,7999", "endLines": "10,14,18,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,51,61,75,75,54,66", "endOffsets": "331,531,722,4447,4535,4615,4708,4801,4874,4941,5043,5141,5209,5276,5341,5410,5529,5648,5763,5836,5915,5990,6059,6142,6224,6290,6355,7064,7122,7170,7231,7296,7358,7423,7491,7549,7607,7673,7725,7787,7863,7939,7994,8061"}}]}]}