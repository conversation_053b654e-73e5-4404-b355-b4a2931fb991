import 'package:flutter/foundation.dart';
import '../models/xtreme_api_models.dart';
import '../services/xtreme_api_service.dart';

class IptvDataProvider extends ChangeNotifier {
  final XtremeApiService _apiService = XtremeApiService();

  // Authentication state
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _errorMessage;
  XtremeAuthResponse? _authResponse;

  // Content data
  List<IptvCategory> _liveCategories = [];
  List<IptvCategory> _vodCategories = [];
  List<IptvCategory> _seriesCategories = [];
  List<LiveChannel> _liveChannels = [];
  List<VodContent> _vodContent = [];
  List<VodContent> _seriesContent = [];

  // Getters
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  XtremeAuthResponse? get authResponse => _authResponse;
  
  List<IptvCategory> get liveCategories => _liveCategories;
  List<IptvCategory> get vodCategories => _vodCategories;
  List<IptvCategory> get seriesCategories => _seriesCategories;
  List<LiveChannel> get liveChannels => _liveChannels;
  List<VodContent> get vodContent => _vodContent;
  List<VodContent> get seriesContent => _seriesContent;

  // Authentication
  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _apiService.authenticate(username, password);
      
      if (success) {
        _isAuthenticated = true;
        _authResponse = _apiService.authResponse;
        
        // Load initial data after successful authentication
        await _loadInitialData();
        
        print('✅ Login successful for user: $username');
        _setLoading(false);
        return true;
      } else {
        _setError('Invalid username or password');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  // Load initial data after authentication
  Future<void> _loadInitialData() async {
    try {
      // Load categories in parallel
      await Future.wait([
        loadLiveCategories(),
        loadVodCategories(),
        loadSeriesCategories(),
      ]);

      // Load some initial content
      if (_liveCategories.isNotEmpty) {
        await loadLiveChannels();
      }
    } catch (e) {
      print('⚠️ Error loading initial data: $e');
    }
  }

  // Load Live TV Categories
  Future<void> loadLiveCategories() async {
    try {
      _liveCategories = await _apiService.getLiveTvCategories();
      notifyListeners();
    } catch (e) {
      print('❌ Error loading live categories: $e');
      _setError('Failed to load live TV categories');
    }
  }

  // Load Live Channels
  Future<void> loadLiveChannels({String? categoryId}) async {
    try {
      _liveChannels = await _apiService.getLiveChannels(categoryId: categoryId);
      notifyListeners();
    } catch (e) {
      print('❌ Error loading live channels: $e');
      _setError('Failed to load live channels');
    }
  }

  // Load VOD Categories
  Future<void> loadVodCategories() async {
    try {
      _vodCategories = await _apiService.getVodCategories();
      notifyListeners();
    } catch (e) {
      print('❌ Error loading VOD categories: $e');
      _setError('Failed to load movie categories');
    }
  }

  // Load VOD Content
  Future<void> loadVodContent({String? categoryId}) async {
    try {
      _vodContent = await _apiService.getVodContent(categoryId: categoryId);
      notifyListeners();
    } catch (e) {
      print('❌ Error loading VOD content: $e');
      _setError('Failed to load movies');
    }
  }

  // Load Series Categories
  Future<void> loadSeriesCategories() async {
    try {
      _seriesCategories = await _apiService.getSeriesCategories();
      notifyListeners();
    } catch (e) {
      print('❌ Error loading series categories: $e');
      _setError('Failed to load series categories');
    }
  }

  // Load Series Content
  Future<void> loadSeriesContent({String? categoryId}) async {
    try {
      _seriesContent = await _apiService.getSeriesContent(categoryId: categoryId);
      notifyListeners();
    } catch (e) {
      print('❌ Error loading series content: $e');
      _setError('Failed to load series');
    }
  }

  // Get stream URL for live channel
  String getLiveStreamUrl(int streamId, {String format = 'ts'}) {
    return _apiService.getLiveStreamUrl(streamId, format: format);
  }

  // Get stream URL for VOD
  String getVodStreamUrl(int streamId, String containerExtension) {
    return _apiService.getVodStreamUrl(streamId, containerExtension);
  }

  // Get stream URL for series
  String getSeriesStreamUrl(int streamId, String containerExtension) {
    return _apiService.getSeriesStreamUrl(streamId, containerExtension);
  }

  // Get EPG data
  Future<List<EpgData>> getEpg(int streamId) async {
    try {
      return await _apiService.getEpg(streamId);
    } catch (e) {
      print('❌ Error loading EPG: $e');
      _setError('Failed to load program guide');
      return [];
    }
  }

  // Search functionality
  List<LiveChannel> searchLiveChannels(String query) {
    if (query.isEmpty) return _liveChannels;
    
    return _liveChannels.where((channel) =>
      channel.name.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  List<VodContent> searchVodContent(String query) {
    if (query.isEmpty) return _vodContent;
    
    return _vodContent.where((content) =>
      content.name.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  List<VodContent> searchSeriesContent(String query) {
    if (query.isEmpty) return _seriesContent;
    
    return _seriesContent.where((content) =>
      content.name.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  // Get content by category
  List<LiveChannel> getLiveChannelsByCategory(String categoryId) {
    return _liveChannels.where((channel) => 
      channel.categoryId == categoryId
    ).toList();
  }

  List<VodContent> getVodContentByCategory(String categoryId) {
    return _vodContent.where((content) => 
      content.categoryId == categoryId
    ).toList();
  }

  List<VodContent> getSeriesContentByCategory(String categoryId) {
    return _seriesContent.where((content) => 
      content.categoryId == categoryId
    ).toList();
  }

  // Logout
  void logout() {
    _apiService.logout();
    _isAuthenticated = false;
    _authResponse = null;
    _clearAllData();
    notifyListeners();
    print('👋 User logged out');
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void _clearAllData() {
    _liveCategories.clear();
    _vodCategories.clear();
    _seriesCategories.clear();
    _liveChannels.clear();
    _vodContent.clear();
    _seriesContent.clear();
    _clearError();
  }

  // Refresh all data
  Future<void> refreshAllData() async {
    if (!_isAuthenticated) return;
    
    _setLoading(true);
    await _loadInitialData();
    _setLoading(false);
  }
}
