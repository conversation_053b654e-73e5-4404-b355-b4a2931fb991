import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';
import '../widgets/simple_tv_focus.dart';
import '../widgets/hero_banner.dart';
import '../widgets/content_row.dart';
import '../widgets/continue_watching_row.dart';
import '../models/channel.dart';
import '../models/user_profile.dart';
import '../data/sample_data.dart';
import '../services/database_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  int _selectedIndex = 0;

  // Sample data - will be replaced with API data later
  final List<Channel> _featuredChannels = SampleData.featuredChannels;
  final List<ChannelCategory> _categories = SampleData.categories;

  // Continue watching data
  List<ContinueWatchingItem> _continueWatchingItems = [];
  final DatabaseService _databaseService = DatabaseService();

  @override
  void initState() {
    super.initState();
    _loadContinueWatching();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadContinueWatching() async {
    try {
      final userProfile = await _databaseService.getUserProfile();
      if (userProfile != null) {
        setState(() {
          _continueWatchingItems = userProfile.continueWatching;
        });
      }
    } catch (e) {
      print('Error loading continue watching: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
            // App Bar
            SliverAppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              floating: true,
              snap: true,
              title: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryRed,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'IPTV',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    'Player',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                ],
              ),
              actions: [
                SimpleTVFocus(
                  child: IconButton(
                    icon: const Icon(Icons.search, color: Colors.white, size: 28),
                    onPressed: () {
                      print("Search pressed");
                    },
                  ),
                ),
                const SizedBox(width: 8),
                SimpleTVFocus(
                  child: IconButton(
                    icon: const Icon(Icons.settings, color: Colors.white, size: 28),
                    onPressed: () {
                      print("Settings pressed");
                    },
                  ),
                ),
                const SizedBox(width: 16),
              ],
            ),
            
            // Hero Banner
            SliverToBoxAdapter(
              child: HeroBanner(
                channel: _featuredChannels[_selectedIndex],
                onPlayPressed: () {
                  // Navigate to player
                },
                onInfoPressed: () {
                  // Show channel info
                },
              ),
            ),
            
            // Content Rows
            ..._categories.map((category) => SliverToBoxAdapter(
              child: ContentRow(
                title: category.name,
                channels: category.channels,
                onChannelPressed: (channel) {
                  // Navigate to player or channel details
                },
              ),
            )).toList(),
            
          // Bottom padding
          const SliverToBoxAdapter(
            child: SizedBox(height: 50),
          ),
        ],
      ),
    );
  }
}
