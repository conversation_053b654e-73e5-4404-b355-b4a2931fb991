import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';

class SimpleTVFocus extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final bool autofocus;

  const SimpleTVFocus({
    Key? key,
    required this.child,
    this.onPressed,
    this.autofocus = false,
  }) : super(key: key);

  @override
  State<SimpleTVFocus> createState() => _SimpleTVFocusState();
}

class _SimpleTVFocusState extends State<SimpleTVFocus> {
  bool _isFocused = false;

  @override
  Widget build(BuildContext context) {
    return Focus(
      autofocus: widget.autofocus,
      canRequestFocus: true,
      onFocusChange: (focused) {
        setState(() {
          _isFocused = focused;
        });
        if (focused) {
          HapticFeedback.selectionClick();
          print("Focus gained on widget");
        }
      },
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          print("Key pressed: ${event.logicalKey}");
          // Handle all possible Android TV remote buttons
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space ||
              event.logicalKey == LogicalKeyboardKey.gameButtonA ||
              event.logicalKey == LogicalKeyboardKey.mediaPlayPause ||
              event.logicalKey == LogicalKeyboardKey.numpadEnter) {
            print("Action button pressed!");
            widget.onPressed?.call();
            HapticFeedback.lightImpact();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onPressed,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()..scale(_isFocused ? 1.08 : 1.0),
          decoration: _isFocused
              ? BoxDecoration(
                  border: Border.all(
                    color: Colors.white,
                    width: 4,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.5),
                      blurRadius: 16,
                      spreadRadius: 4,
                    ),
                  ],
                )
              : null,
          child: widget.child,
        ),
      ),
    );
  }
}

// Simple button for TV
class TVButton extends StatelessWidget {
  final String text;
  final IconData? icon;
  final VoidCallback? onPressed;
  final bool autofocus;
  final Color? backgroundColor;
  final Color? textColor;

  const TVButton({
    Key? key,
    required this.text,
    this.icon,
    this.onPressed,
    this.autofocus = false,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SimpleTVFocus(
      autofocus: autofocus,
      onPressed: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        decoration: BoxDecoration(
          color: backgroundColor ?? AppTheme.primaryRed,
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: textColor ?? Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: TextStyle(
                color: textColor ?? Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Simple card for TV
class TVCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final bool autofocus;
  final double? width;
  final double? height;

  const TVCard({
    Key? key,
    required this.child,
    this.onPressed,
    this.autofocus = false,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SimpleTVFocus(
      autofocus: autofocus,
      onPressed: onPressed,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: AppTheme.cardBackground,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: child,
      ),
    );
  }
}
