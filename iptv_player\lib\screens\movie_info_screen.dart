import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/xtreme_api_models.dart';
import '../models/user_profile.dart';
import '../services/local_database_service.dart';
import '../services/xtreme_api_service.dart';
import '../widgets/directional_focus_widget.dart';
import 'video_player_screen.dart';

class MovieInfoScreen extends StatefulWidget {
  final VodContent movie;

  const MovieInfoScreen({
    Key? key,
    required this.movie,
  }) : super(key: key);

  @override
  State<MovieInfoScreen> createState() => _MovieInfoScreenState();
}

class _MovieInfoScreenState extends State<MovieInfoScreen> {
  final LocalDatabaseService _dbService = LocalDatabaseService();
  final XtremeApiService _apiService = XtremeApiService();

  UserProfile? _currentProfile;
  bool _isInMyList = false;
  bool _isLoading = false;
  bool _isLoadingMovieInfo = true;

  Map<String, dynamic>? _movieInfo;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadCurrentProfile();
    _loadMovieInfo();
  }

  Future<void> _loadCurrentProfile() async {
    try {
      final profile = await _dbService.getCurrentProfile();
      setState(() {
        _currentProfile = profile;
        _isInMyList = profile?.favoriteMovies.contains(widget.movie.streamId.toString()) ?? false;
      });
    } catch (e) {
      print('Error loading current profile: $e');
    }
  }

  Future<void> _loadMovieInfo() async {
    try {
      setState(() {
        _isLoadingMovieInfo = true;
        _error = null;
      });

      final movieInfo = await _apiService.getVodInfo(widget.movie.streamId);

      setState(() {
        _movieInfo = movieInfo;
        _isLoadingMovieInfo = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoadingMovieInfo = false;
      });
      print('Error loading movie info: $e');
    }
  }

  Future<void> _toggleMyList() async {
    if (_currentProfile == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final movieId = widget.movie.streamId.toString();
      List<String> favoriteMovies = List.from(_currentProfile!.favoriteMovies);

      if (_isInMyList) {
        favoriteMovies.remove(movieId);
        print('🗑️ Removed ${widget.movie.name} from My List');
      } else {
        favoriteMovies.add(movieId);
        print('❤️ Added ${widget.movie.name} to My List');
      }

      final updatedProfile = _currentProfile!.copyWith(
        favoriteMovies: favoriteMovies,
        lastUsed: DateTime.now(),
      );

      await _dbService.saveProfile(updatedProfile);
      await _dbService.setCurrentProfile(updatedProfile.id);

      setState(() {
        _currentProfile = updatedProfile;
        _isInMyList = !_isInMyList;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Error updating My List: $e');
    }
  }

  void _watchMovie() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(movie: widget.movie),
      ),
    );
  }

  void _goBack() {
    Navigator.of(context).pop();
  }

  // Safe method to extract year from release date
  String _extractYear(String releaseDate) {
    try {
      if (releaseDate.isEmpty) return 'N/A';

      // Try to extract year from different formats
      if (releaseDate.length >= 4) {
        // Check if it starts with a year (YYYY-MM-DD format)
        final yearMatch = RegExp(r'^\d{4}').firstMatch(releaseDate);
        if (yearMatch != null) {
          return yearMatch.group(0)!;
        }

        // Fallback: take first 4 characters if they're digits
        final first4 = releaseDate.substring(0, 4);
        if (RegExp(r'^\d{4}$').hasMatch(first4)) {
          return first4;
        }
      }

      // If all else fails, return the original string (truncated if too long)
      return releaseDate.length > 10 ? releaseDate.substring(0, 10) : releaseDate;
    } catch (e) {
      print('Error extracting year from: $releaseDate - $e');
      return 'N/A';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: Stack(
        children: [
          // Full screen movie backdrop
          _buildMovieBackdrop(),
          
          // Movie details overlay
          _buildMovieDetails(),
          
          // Back button
          _buildBackButton(),
        ],
      ),
    );
  }

  Widget _buildMovieBackdrop() {
    // Use backdrop_path if available, otherwise use movie_image, otherwise use streamIcon
    String? imageUrl;

    if (_movieInfo != null) {
      final info = _movieInfo!['info'];
      if (info != null) {
        // Try backdrop first
        if (info['backdrop_path'] != null && info['backdrop_path'] is List && info['backdrop_path'].isNotEmpty) {
          imageUrl = info['backdrop_path'][0];
        }
        // Fallback to movie_image
        else if (info['movie_image'] != null && info['movie_image'].toString().isNotEmpty) {
          imageUrl = info['movie_image'];
        }
      }
    }

    // Final fallback to streamIcon
    imageUrl ??= widget.movie.streamIcon.isNotEmpty ? widget.movie.streamIcon : null;

    return Container(
      width: double.infinity,
      height: double.infinity,
      child: imageUrl != null
          ? Image.network(
              imageUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: const Color(0xFF2D2D44),
                  child: const Center(
                    child: Icon(
                      Icons.movie,
                      color: Colors.white54,
                      size: 120,
                    ),
                  ),
                );
              },
            )
          : Container(
              color: const Color(0xFF2D2D44),
              child: const Center(
                child: Icon(
                  Icons.movie,
                  color: Colors.white54,
                  size: 120,
                ),
              ),
            ),
    );
  }

  Widget _buildMovieDetails() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.3),
            Colors.black.withOpacity(0.7),
            Colors.black.withOpacity(0.9),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isLoadingMovieInfo) ...[
              // Loading state
              const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'Loading movie details...',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 18,
                ),
                textAlign: TextAlign.center,
              ),
            ] else if (_error != null) ...[
              // Error state
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 20),
              Text(
                'Failed to load movie details',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                widget.movie.name,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 18,
                ),
              ),
            ] else ...[
              // Movie title
              Text(
                _movieInfo?['info']?['name'] ?? widget.movie.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(2, 2),
                      blurRadius: 4,
                      color: Colors.black,
                    ),
                  ],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 20),

              // Movie info row
              _buildMovieInfoRow(),

              const SizedBox(height: 20),

              // Plot/Description
              Text(
                _movieInfo?['info']?['plot'] ?? 'Experience this amazing movie with stunning visuals and captivating storytelling. Watch now and enjoy an unforgettable cinematic journey.',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  height: 1.5,
                ),
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 30),

              // Action buttons
              _buildActionButtons(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMovieInfoRow() {
    final info = _movieInfo?['info'];

    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: [
        // Release Year
        if (info?['releasedate'] != null) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              _extractYear(info['releasedate'].toString()), // Safe year extraction
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],

        // Duration
        if (info?['duration'] != null && info['duration'].toString().isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              info['duration'].toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],

        // Genre
        if (info?['genre'] != null && info['genre'].toString().isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: const Color(0xFFE17055).withOpacity(0.8),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              info['genre'].toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],

        // Rating
        if (info?['rating'] != null && info['rating'].toString().isNotEmpty && info['rating'].toString() != '0') ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.8),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  info['rating'].toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // Watch Movie button
        DirectionalFocusWidget(
          autofocus: true,
          debugLabel: "Watch Movie",
          borderRadius: BorderRadius.circular(8),
          onPressed: _watchMovie,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            decoration: BoxDecoration(
              color: const Color(0xFFE17055),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'Watch Movie',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(width: 20),
        
        // Add to My List button
        DirectionalFocusWidget(
          debugLabel: _isInMyList ? "Remove from My List" : "Add to My List",
          borderRadius: BorderRadius.circular(8),
          onPressed: _isLoading ? null : _toggleMyList,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            decoration: BoxDecoration(
              color: _isInMyList 
                  ? Colors.white.withOpacity(0.2)
                  : const Color(0xFF3D3D5C),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Icon(
                        _isInMyList ? Icons.check : Icons.add,
                        color: Colors.white,
                        size: 24,
                      ),
                const SizedBox(width: 8),
                Text(
                  _isInMyList ? 'In My List' : 'My List',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBackButton() {
    return Positioned(
      top: 40,
      left: 40,
      child: DirectionalFocusWidget(
        debugLabel: "Back",
        borderRadius: BorderRadius.circular(25),
        onPressed: _goBack,
        child: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.5),
            borderRadius: BorderRadius.circular(25),
          ),
          child: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }
}
