com.example.iptv_player.app-preference-1.2.1-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0098a6e93522fecc805d8900172003dc\transformed\preference-1.2.1\res
com.example.iptv_player.app-jetified-datastore-preferences-release-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02cddce53ae6e54afa639a8fcdb8b322\transformed\jetified-datastore-preferences-release\res
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\058b46d59488c77d02cd8814b15f89a0\transformed\jetified-media3-ui-1.4.1\res
com.example.iptv_player.app-jetified-activity-1.8.1-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13dd610fda78ecd8ad3daad9b8195d7e\transformed\jetified-activity-1.8.1\res
com.example.iptv_player.app-lifecycle-viewmodel-2.7.0-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b91be3af319ede480d7185430690ee1\transformed\lifecycle-viewmodel-2.7.0\res
com.example.iptv_player.app-jetified-datastore-core-release-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba8599b6014e514325b6a37b5077642\transformed\jetified-datastore-core-release\res
com.example.iptv_player.app-jetified-lifecycle-runtime-ktx-2.7.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1da3d52bf544e2149dd46ce2061a20cf\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.iptv_player.app-jetified-tracing-1.2.0-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\285b6d6abe2dd4ba9515886e610a7341\transformed\jetified-tracing-1.2.0\res
com.example.iptv_player.app-media-1.7.0-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c7a28485314ee0e74d343b62dcecbb6\transformed\media-1.7.0\res
com.example.iptv_player.app-jetified-fragment-ktx-1.7.1-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e4c2e4e231b6a1dd462fb9cdc4ff977\transformed\jetified-fragment-ktx-1.7.1\res
com.example.iptv_player.app-browser-1.8.0-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\308a2e77faa557d0bd706972416bde6a\transformed\browser-1.8.0\res
com.example.iptv_player.app-jetified-appcompat-resources-1.1.0-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b0283ed69cc5a036226586199afb07\transformed\jetified-appcompat-resources-1.1.0\res
com.example.iptv_player.app-appcompat-1.1.0-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a4dcd3e8c92efa77cfa193b658940d9\transformed\appcompat-1.1.0\res
com.example.iptv_player.app-transition-1.4.1-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45af1ebc35cbf9d2d2886a132166b73a\transformed\transition-1.4.1\res
com.example.iptv_player.app-jetified-core-1.0.0-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47506b70b93177473c53f4d5d7e77c91\transformed\jetified-core-1.0.0\res
com.example.iptv_player.app-jetified-window-1.2.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\res
com.example.iptv_player.app-fragment-1.7.1-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55520e4df2220e27f13f0bbb7467d11a\transformed\fragment-1.7.1\res
com.example.iptv_player.app-jetified-activity-ktx-1.8.1-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\594d81b2ca4ffa5071fc50a56b570e77\transformed\jetified-activity-ktx-1.8.1\res
com.example.iptv_player.app-jetified-profileinstaller-1.3.1-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\res
com.example.iptv_player.app-jetified-core-ktx-1.13.1-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec70d4eb0351866340efe37c2014fef\transformed\jetified-core-ktx-1.13.1\res
com.example.iptv_player.app-jetified-customview-poolingcontainer-1.0.0-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7133f88ce0019484b20b8b2d5cf67e46\transformed\jetified-customview-poolingcontainer-1.0.0\res
com.example.iptv_player.app-slidingpanelayout-1.2.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74ed8ceaa55f9ae57c7b49acd30346c2\transformed\slidingpanelayout-1.2.0\res
com.example.iptv_player.app-recyclerview-1.3.0-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7727f73cc05fc3444e6c684d91d2505f\transformed\recyclerview-1.3.0\res
com.example.iptv_player.app-core-1.13.1-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\res
com.example.iptv_player.app-jetified-lifecycle-viewmodel-ktx-2.7.0-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1682fe25d19efcf6ed57ec9bfdc01f5\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.iptv_player.app-jetified-media3-exoplayer-1.4.1-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab1dc0cd23ebe3c890248eaabfbb4ea4\transformed\jetified-media3-exoplayer-1.4.1\res
com.example.iptv_player.app-jetified-savedstate-1.2.1-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d51a44ab6b56289d4858158a1ad6dd\transformed\jetified-savedstate-1.2.1\res
com.example.iptv_player.app-lifecycle-runtime-2.7.0-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c092edbccc16347970ed4f22e8da111a\transformed\lifecycle-runtime-2.7.0\res
com.example.iptv_player.app-jetified-window-java-1.2.0-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f85211ae6978d0218d51595a7f7e62\transformed\jetified-window-java-1.2.0\res
com.example.iptv_player.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7156a54e8f0502a1f8f57797d8b3e6e\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.iptv_player.app-jetified-datastore-release-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7cbd00988e28f46cac66dd959ae6532\transformed\jetified-datastore-release\res
com.example.iptv_player.app-jetified-startup-runtime-1.1.1-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\res
com.example.iptv_player.app-lifecycle-livedata-2.7.0-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0eede8414d1ee0e5f236daeb1a2e59a\transformed\lifecycle-livedata-2.7.0\res
com.example.iptv_player.app-lifecycle-livedata-core-2.7.0-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e21678784b3eef6b33f576b1691a56\transformed\lifecycle-livedata-core-2.7.0\res
com.example.iptv_player.app-jetified-lifecycle-process-2.7.0-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\res
com.example.iptv_player.app-jetified-savedstate-ktx-1.2.1-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebca0abd79b4e189ce4efe6f41f957a4\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.iptv_player.app-jetified-lifecycle-livedata-core-ktx-2.7.0-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebeb29d7113d60e59d5e0acf92f847f3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.iptv_player.app-jetified-annotation-experimental-1.4.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09a22cdd213028a38958888c7d16e20\transformed\jetified-annotation-experimental-1.4.0\res
com.example.iptv_player.app-core-runtime-2.2.0-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff849810dfe34a0ab790c402787c149e\transformed\core-runtime-2.2.0\res
com.example.iptv_player.app-debug-39 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\debug\res
com.example.iptv_player.app-main-40 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res
com.example.iptv_player.app-pngs-41 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\generated\res\pngs\debug
com.example.iptv_player.app-resValues-42 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\generated\res\resValues\debug
com.example.iptv_player.app-packageDebugResources-43 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.iptv_player.app-packageDebugResources-44 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.iptv_player.app-debug-45 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.iptv_player.app-debug-46 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\package_info_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.iptv_player.app-debug-47 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.iptv_player.app-debug-48 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.example.iptv_player.app-debug-49 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\sqflite_android\intermediates\packaged_res\debug\packageDebugResources
com.example.iptv_player.app-debug-50 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
com.example.iptv_player.app-debug-51 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\video_player_android\intermediates\packaged_res\debug\packageDebugResources
com.example.iptv_player.app-debug-52 C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\wakelock_plus\intermediates\packaged_res\debug\packageDebugResources
