import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NavigationController extends ChangeNotifier {
  int _currentRowIndex = 0;
  int _currentItemIndex = 0;
  final List<int> _itemCounts = [];

  int get currentRowIndex => _currentRowIndex;
  int get currentItemIndex => _currentItemIndex;

  void setRowCount(int rowIndex, int itemCount) {
    while (_itemCounts.length <= rowIndex) {
      _itemCounts.add(0);
    }
    _itemCounts[rowIndex] = itemCount;
  }

  void moveUp() {
    if (_currentRowIndex > 0) {
      _currentRowIndex--;
      // Ensure current item index is valid for the new row
      if (_currentItemIndex >= _itemCounts[_currentRowIndex]) {
        _currentItemIndex = _itemCounts[_currentRowIndex] - 1;
      }
      notifyListeners();
    }
  }

  void moveDown() {
    if (_currentRowIndex < _itemCounts.length - 1) {
      _currentRowIndex++;
      // Ensure current item index is valid for the new row
      if (_currentItemIndex >= _itemCounts[_currentRowIndex]) {
        _currentItemIndex = _itemCounts[_currentRowIndex] - 1;
      }
      notifyListeners();
    }
  }

  void moveLeft() {
    if (_currentItemIndex > 0) {
      _currentItemIndex--;
      notifyListeners();
    }
  }

  void moveRight() {
    if (_currentRowIndex < _itemCounts.length && 
        _currentItemIndex < _itemCounts[_currentRowIndex] - 1) {
      _currentItemIndex++;
      notifyListeners();
    }
  }

  void setPosition(int rowIndex, int itemIndex) {
    _currentRowIndex = rowIndex;
    _currentItemIndex = itemIndex;
    notifyListeners();
  }

  bool isCurrentPosition(int rowIndex, int itemIndex) {
    return _currentRowIndex == rowIndex && _currentItemIndex == itemIndex;
  }
}

class TVNavigationWidget extends StatefulWidget {
  final Widget child;
  final NavigationController? controller;

  const TVNavigationWidget({
    Key? key,
    required this.child,
    this.controller,
  }) : super(key: key);

  @override
  State<TVNavigationWidget> createState() => _TVNavigationWidgetState();
}

class _TVNavigationWidgetState extends State<TVNavigationWidget> {
  late NavigationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? NavigationController();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      autofocus: true,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          switch (event.logicalKey) {
            case LogicalKeyboardKey.arrowUp:
              _controller.moveUp();
              return KeyEventResult.handled;
            case LogicalKeyboardKey.arrowDown:
              _controller.moveDown();
              return KeyEventResult.handled;
            case LogicalKeyboardKey.arrowLeft:
              _controller.moveLeft();
              return KeyEventResult.handled;
            case LogicalKeyboardKey.arrowRight:
              _controller.moveRight();
              return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: widget.child,
    );
  }
}

// Enhanced Focus Widget with better TV navigation
class TVFocusableWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final VoidCallback? onLongPress;
  final VoidCallback? onFocused;
  final VoidCallback? onUnfocused;
  final bool autofocus;
  final int rowIndex;
  final int itemIndex;
  final NavigationController? navigationController;

  const TVFocusableWidget({
    Key? key,
    required this.child,
    this.onPressed,
    this.onLongPress,
    this.onFocused,
    this.onUnfocused,
    this.autofocus = false,
    required this.rowIndex,
    required this.itemIndex,
    this.navigationController,
  }) : super(key: key);

  @override
  State<TVFocusableWidget> createState() => _TVFocusableWidgetState();
}

class _TVFocusableWidgetState extends State<TVFocusableWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _focusNode.addListener(_onFocusChange);
    
    // Listen to navigation controller if provided
    widget.navigationController?.addListener(_onNavigationChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    widget.navigationController?.removeListener(_onNavigationChange);
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onNavigationChange() {
    final shouldBeFocused = widget.navigationController?.isCurrentPosition(
      widget.rowIndex,
      widget.itemIndex,
    ) ?? false;

    if (shouldBeFocused && !_focusNode.hasFocus) {
      _focusNode.requestFocus();
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });

    if (_isFocused) {
      _animationController.forward();
      widget.onFocused?.call();
      widget.navigationController?.setPosition(widget.rowIndex, widget.itemIndex);
      HapticFeedback.selectionClick();
    } else {
      _animationController.reverse();
      widget.onUnfocused?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            widget.onPressed?.call();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onPressed,
        onLongPress: widget.onLongPress,
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                decoration: _isFocused
                    ? BoxDecoration(
                        border: Border.all(
                          color: Colors.white,
                          width: 3,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      )
                    : null,
                child: widget.child,
              ),
            );
          },
        ),
      ),
    );
  }
}
