import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../models/xtreme_api_models.dart';
import '../services/xtreme_api_service.dart';

class DownscaledVideoPlayerScreen extends StatefulWidget {
  final VodContent movie;

  const DownscaledVideoPlayerScreen({
    Key? key,
    required this.movie,
  }) : super(key: key);

  @override
  State<DownscaledVideoPlayerScreen> createState() => _DownscaledVideoPlayerScreenState();
}

class _DownscaledVideoPlayerScreenState extends State<DownscaledVideoPlayerScreen> {
  final XtremeApiService _apiService = XtremeApiService();
  
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  int _resolutionAttempt = 0;
  String _currentResolution = 'Original';

  // List of resolutions to try (from lowest to highest)
  final List<Map<String, String>> _resolutionOptions = [
    {'name': '480p', 'value': '854x480', 'quality': 'low'},
    {'name': '720p', 'value': '1280x720', 'quality': 'medium'},
    {'name': 'Original', 'value': 'original', 'quality': 'high'},
  ];

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _keepScreenOn();
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoPlayerController?.dispose();
    WakelockPlus.disable();
    super.dispose();
  }

  Future<void> _keepScreenOn() async {
    await WakelockPlus.enable();
  }

  Future<void> _initializePlayer() async {
    // Start with the lowest resolution for best compatibility
    await _tryResolution(_resolutionAttempt);
  }

  Future<void> _tryResolution(int attemptIndex) async {
    if (attemptIndex >= _resolutionOptions.length) {
      // All resolutions failed
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'This video is not compatible with your device. We tried multiple resolutions (480p, 720p, Original) but none worked.';
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _currentResolution = _resolutionOptions[attemptIndex]['name']!;
      });

      String streamUrl;
      final resolution = _resolutionOptions[attemptIndex];
      
      print('🎬 Trying resolution: ${resolution['name']} for: ${widget.movie.name}');

      // Generate URL based on resolution
      if (resolution['value'] == 'original') {
        // Original resolution
        streamUrl = _apiService.getVodStreamUrl(
          widget.movie.streamId,
          widget.movie.containerExtension,
        );
      } else {
        // Try with resolution parameter first
        streamUrl = _apiService.getVodStreamUrlWithResolution(
          widget.movie.streamId,
          widget.movie.containerExtension,
          resolution['value']!,
        );
      }

      print('📺 Stream URL (${resolution['name']}): $streamUrl');

      // Initialize video player controller with resolution-specific settings
      _videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(streamUrl),
        videoPlayerOptions: VideoPlayerOptions(
          allowBackgroundPlayback: true,
          mixWithOthers: false,
        ),
        httpHeaders: {
          'User-Agent': 'Mozilla/5.0 (Linux; Android 10; Android TV) AppleWebKit/537.36',
          'Accept': '*/*',
          'Connection': 'keep-alive',
          'X-Requested-Resolution': resolution['value']!,
          'X-Quality': resolution['quality']!,
        },
        formatHint: VideoFormat.other,
      );

      await _videoPlayerController!.initialize();

      // Initialize Chewie controller
      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: true,
        looping: false,
        allowFullScreen: false,
        allowMuting: true,
        allowPlaybackSpeedChanging: false,
        showControlsOnInitialize: true,
        hideControlsTimer: const Duration(seconds: 3),
        materialProgressColors: ChewieProgressColors(
          playedColor: const Color(0xFFE17055),
          handleColor: const Color(0xFFE17055),
          bufferedColor: Colors.white38,
          backgroundColor: Colors.white24,
        ),
        errorBuilder: (context, errorMessage) {
          return _buildErrorWidget(errorMessage);
        },
      );

      setState(() {
        _isLoading = false;
      });

      print('✅ Video player initialized successfully with ${resolution['name']} resolution');
    } catch (e) {
      print('❌ Resolution ${_resolutionOptions[attemptIndex]['name']} failed: $e');
      
      // Dispose current controller before trying next resolution
      _chewieController?.dispose();
      _videoPlayerController?.dispose();
      
      // Try next resolution
      _resolutionAttempt++;
      await _tryResolution(_resolutionAttempt);
    }
  }

  Widget _buildErrorWidget(String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 64,
          ),
          const SizedBox(height: 20),
          const Text(
            'Video Error',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Text(
              errorMessage,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  void _goBack() {
    Navigator.of(context).pop();
  }

  void _retry() {
    _resolutionAttempt = 0;
    _initializePlayer();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.goBack ||
                event.logicalKey == LogicalKeyboardKey.escape) {
              _goBack();
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: Stack(
          children: [
            _buildVideoPlayer(),
            if (_hasError) _buildBackButton(),
            if (_isLoading) _buildResolutionIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            ),
            SizedBox(height: 20),
            Text(
              'Optimizing video quality...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 20),
            const Text(
              'Playback Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                _errorMessage ?? 'Unknown error',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _retry,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFE17055),
                  ),
                  child: const Text(
                    'Try Again',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 20),
                ElevatedButton(
                  onPressed: _goBack,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                  ),
                  child: const Text(
                    'Go Back',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    if (_chewieController != null) {
      return Center(
        child: Chewie(
          controller: _chewieController!,
        ),
      );
    }

    return const Center(
      child: Text(
        'Video not available',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
      ),
    );
  }

  Widget _buildResolutionIndicator() {
    return Positioned(
      top: 40,
      right: 40,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          'Trying $_currentResolution',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return Positioned(
      top: 40,
      left: 40,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(25),
        ),
        child: IconButton(
          onPressed: _goBack,
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }
}
