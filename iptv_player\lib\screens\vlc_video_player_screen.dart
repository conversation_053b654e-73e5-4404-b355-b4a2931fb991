import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../models/xtreme_api_models.dart';
import '../services/xtreme_api_service.dart';

class VlcVideoPlayerScreen extends StatefulWidget {
  final VodContent movie;

  const VlcVideoPlayerScreen({
    Key? key,
    required this.movie,
  }) : super(key: key);

  @override
  State<VlcVideoPlayerScreen> createState() => _VlcVideoPlayerScreenState();
}

class _VlcVideoPlayerScreenState extends State<VlcVideoPlayerScreen> {
  final XtremeApiService _apiService = XtremeApiService();
  
  VlcPlayerController? _vlcController;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  bool _showControls = true;
  bool _isPlaying = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  
  // Control visibility timer
  DateTime _lastInteraction = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _keepScreenOn();
    _hideControlsAfterDelay();
  }

  @override
  void dispose() {
    _vlcController?.dispose();
    WakelockPlus.disable();
    super.dispose();
  }

  Future<void> _keepScreenOn() async {
    await WakelockPlus.enable();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Get the video stream URL
      final streamUrl = _apiService.getVodStreamUrl(
        widget.movie.streamId,
        widget.movie.containerExtension,
      );

      print('🎬 Initializing VLC Player for: ${widget.movie.name}');
      print('📺 Stream URL: $streamUrl');

      // Initialize VLC controller with simple IPTV optimizations
      _vlcController = VlcPlayerController.network(
        streamUrl,
        hwAcc: HwAcc.full, // Use hardware acceleration when available
        autoPlay: true,
        options: VlcPlayerOptions(
          advanced: VlcAdvancedOptions([
            VlcAdvancedOptions.networkCaching(3000), // 3 second buffer
            VlcAdvancedOptions.clockJitter(0),
          ]),
          http: VlcHttpOptions([
            VlcHttpOptions.httpUserAgent('VLC/3.0.16 LibVLC/3.0.16 (Android TV)'),
          ]),
          rtp: VlcRtpOptions([
            VlcRtpOptions.rtpOverRtsp(true),
          ]),
          video: VlcVideoOptions([
            VlcVideoOptions.dropLateFrames(true),
            VlcVideoOptions.skipFrames(true),
          ]),
        ),
      );

      // Add event listeners
      _vlcController!.addOnInitListener(() {
        print('✅ VLC Player initialized successfully');
        setState(() {
          _isLoading = false;
        });
      });

      _vlcController!.addOnRendererEventListener((type, id, name) {
        print('🎬 VLC Renderer Event: $type');
      });

      _vlcController!.addListener(() {
        if (_vlcController != null) {
          setState(() {
            _isPlaying = _vlcController!.value.isPlaying;
            _position = _vlcController!.value.position;
            _duration = _vlcController!.value.duration;
          });
        }
      });

      print('✅ VLC Player setup completed');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = _getErrorMessage(e.toString());
      });
      print('❌ Error initializing VLC Player: $e');
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('network')) {
      return 'Unable to connect to video stream. Please check your internet connection.';
    } else if (error.contains('format')) {
      return 'Video format not supported. Please try a different movie.';
    } else {
      return 'Unable to play video. Please try again or select a different movie.';
    }
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted && DateTime.now().difference(_lastInteraction).inSeconds >= 4) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
      _lastInteraction = DateTime.now();
    });
    _hideControlsAfterDelay();
  }

  void _togglePlayPause() {
    if (_vlcController != null) {
      if (_isPlaying) {
        _vlcController!.pause();
      } else {
        _vlcController!.play();
      }
      _showControlsTemporarily();
    }
  }

  void _seekForward() {
    if (_vlcController != null) {
      final newPosition = _position + const Duration(seconds: 10);
      if (newPosition < _duration) {
        _vlcController!.seekTo(newPosition);
      }
      _showControlsTemporarily();
    }
  }

  void _seekBackward() {
    if (_vlcController != null) {
      final newPosition = _position - const Duration(seconds: 10);
      if (newPosition > Duration.zero) {
        _vlcController!.seekTo(newPosition);
      } else {
        _vlcController!.seekTo(Duration.zero);
      }
      _showControlsTemporarily();
    }
  }

  void _goBack() {
    Navigator.of(context).pop();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            _showControlsTemporarily();
            
            // Handle remote control keys
            if (event.logicalKey == LogicalKeyboardKey.select ||
                event.logicalKey == LogicalKeyboardKey.enter ||
                event.logicalKey == LogicalKeyboardKey.space) {
              _togglePlayPause();
              return KeyEventResult.handled;
            }
            
            if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
              _seekForward();
              return KeyEventResult.handled;
            }
            
            if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
              _seekBackward();
              return KeyEventResult.handled;
            }
            
            if (event.logicalKey == LogicalKeyboardKey.goBack ||
                event.logicalKey == LogicalKeyboardKey.escape) {
              _goBack();
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: GestureDetector(
          onTap: _showControlsTemporarily,
          child: Stack(
            children: [
              // VLC Video player
              _buildVideoPlayer(),
              
              // Controls overlay
              if (_showControls) _buildControlsOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            ),
            SizedBox(height: 20),
            Text(
              'Loading with VLC Player...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'Superior codec support for all formats',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 20),
            const Text(
              'VLC Player Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                _errorMessage ?? 'Unknown error',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _initializePlayer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFE17055),
                  ),
                  child: const Text(
                    'Retry',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 20),
                ElevatedButton(
                  onPressed: _goBack,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                  ),
                  child: const Text(
                    'Go Back',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    if (_vlcController != null) {
      return VlcPlayer(
        controller: _vlcController!,
        aspectRatio: 16 / 9,
        placeholder: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
          ),
        ),
      );
    }

    return const Center(
      child: Text(
        'Video not available',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
      ),
    );
  }

  Widget _buildControlsOverlay() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          // Top controls
          _buildTopControls(),
          
          const Spacer(),
          
          // Center play/pause button
          _buildCenterControls(),
          
          const Spacer(),
          
          // Bottom controls
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: _goBack,
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 32,
            ),
          ),
          
          const SizedBox(width: 20),
          
          // Movie title
          Expanded(
            child: Text(
              widget.movie.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          
          // VLC indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.8),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              'VLC',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Seek backward
        IconButton(
          onPressed: _seekBackward,
          icon: const Icon(
            Icons.replay_10,
            color: Colors.white,
            size: 48,
          ),
        ),
        
        const SizedBox(width: 40),
        
        // Play/Pause
        IconButton(
          onPressed: _togglePlayPause,
          icon: Icon(
            _isPlaying ? Icons.pause : Icons.play_arrow,
            color: Colors.white,
            size: 64,
          ),
        ),
        
        const SizedBox(width: 40),
        
        // Seek forward
        IconButton(
          onPressed: _seekForward,
          icon: const Icon(
            Icons.forward_10,
            color: Colors.white,
            size: 48,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomControls() {
    final progress = _duration.inMilliseconds > 0 
        ? _position.inMilliseconds / _duration.inMilliseconds 
        : 0.0;

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Progress bar
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withOpacity(0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            minHeight: 4,
          ),
          
          const SizedBox(height: 10),
          
          // Time indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(_position),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              Text(
                _formatDuration(_duration),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
