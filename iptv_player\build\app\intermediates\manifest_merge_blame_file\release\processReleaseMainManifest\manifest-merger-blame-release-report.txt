1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.iptv_player"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Android TV permissions -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:4:5-79
12-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:4:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:5:5-68
13-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:5:22-65
14
15    <!-- Declare this app is designed for TV -->
16    <uses-feature
16-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:8:5-10:35
17        android:name="android.software.leanback"
17-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:9:9-49
18        android:required="true" />
18-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:10:9-32
19
20    <!-- Declare touchscreen not required -->
21    <uses-feature
21-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:13:5-15:36
22        android:name="android.hardware.touchscreen"
22-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:14:9-52
23        android:required="false" />
23-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:15:9-33
24    <!--
25         Required to query activities that can process text, see:
26         https://developer.android.com/training/package-visibility and
27         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
28
29         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
30    -->
31    <queries>
31-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:66:5-71:15
32        <intent>
32-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:67:9-70:18
33            <action android:name="android.intent.action.PROCESS_TEXT" />
33-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:68:13-72
33-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:68:21-70
34
35            <data android:mimeType="text/plain" />
35-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:69:13-50
35-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:69:19-48
36        </intent>
37    </queries>
38
39    <permission
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
40        android:name="com.example.iptv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
41        android:protectionLevel="signature" />
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
42
43    <uses-permission android:name="com.example.iptv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
44
45    <application
46        android:name="android.app.Application"
46-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:19:9-42
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:extractNativeLibs="true"
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:20:9-43
50        android:label="iptv_player"
50-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:18:9-36
51        android:networkSecurityConfig="@xml/network_security_config"
51-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:22:9-69
52        android:usesCleartextTraffic="true" >
52-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:21:9-44
53        <activity
53-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:23:9-45:20
54            android:name="com.example.iptv_player.MainActivity"
54-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:24:13-41
55            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
55-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:29:13-163
56            android:exported="true"
56-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:25:13-36
57            android:hardwareAccelerated="true"
57-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:30:13-47
58            android:launchMode="singleTop"
58-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:26:13-43
59            android:taskAffinity=""
59-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:27:13-36
60            android:theme="@style/LaunchTheme"
60-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:28:13-47
61            android:windowSoftInputMode="adjustResize" >
61-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:31:13-55
62
63            <!--
64                 Specifies an Android theme to apply to this Activity as soon as
65                 the Android process has started. This theme is visible to the user
66                 while the Flutter UI initializes. After that, this theme continues
67                 to determine the Window background behind the Flutter UI.
68            -->
69            <meta-data
69-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:36:13-39:17
70                android:name="io.flutter.embedding.android.NormalTheme"
70-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:37:15-70
71                android:resource="@style/NormalTheme" />
71-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:38:15-52
72
73            <intent-filter>
73-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:40:13-44:29
74                <action android:name="android.intent.action.MAIN" />
74-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:41:17-68
74-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:41:25-66
75
76                <category android:name="android.intent.category.LAUNCHER" />
76-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:42:17-76
76-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:42:27-74
77                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
77-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:43:17-85
77-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:43:27-83
78            </intent-filter>
79        </activity>
80
81        <!-- ExoPlayer Activity for native video playback -->
82        <activity
82-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:48:9-53:46
83            android:name="com.example.iptv_player.ExoPlayerActivity"
83-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:49:13-46
84            android:configChanges="orientation|keyboardHidden|screenSize"
84-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:52:13-74
85            android:exported="false"
85-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:50:13-37
86            android:launchMode="singleTop"
86-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:53:13-43
87            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
87-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:51:13-77
88
89        <!--
90             Don't delete the meta-data below.
91             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
92        -->
93        <meta-data
93-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:57:9-59:33
94            android:name="flutterEmbedding"
94-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:58:13-44
95            android:value="2" />
95-->C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\AndroidManifest.xml:59:13-30
96
97        <activity
97-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
98            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
98-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
99            android:exported="false"
99-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
100            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
100-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
101
102        <uses-library
102-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
103            android:name="androidx.window.extensions"
103-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
104            android:required="false" />
104-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
105        <uses-library
105-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
106            android:name="androidx.window.sidecar"
106-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
107            android:required="false" />
107-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
108
109        <provider
109-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
110            android:name="androidx.startup.InitializationProvider"
110-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
111            android:authorities="com.example.iptv_player.androidx-startup"
111-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
112            android:exported="false" >
112-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
113            <meta-data
113-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
115                android:value="androidx.startup" />
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
116            <meta-data
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
118                android:value="androidx.startup" />
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
119        </provider>
120
121        <receiver
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
122            android:name="androidx.profileinstaller.ProfileInstallReceiver"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
123            android:directBootAware="false"
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
124            android:enabled="true"
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
125            android:exported="true"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
126            android:permission="android.permission.DUMP" >
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
128                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
131                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
134                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
137                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
138            </intent-filter>
139        </receiver>
140    </application>
141
142</manifest>
