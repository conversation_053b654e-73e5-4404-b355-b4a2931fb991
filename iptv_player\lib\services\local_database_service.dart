import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile.dart';

class LocalDatabaseService {
  static const String _profilesKey = 'user_profiles';
  static const String _currentProfileKey = 'current_profile_id';
  static const String _lastLoginKey = 'last_login_credentials';

  // Singleton pattern
  static final LocalDatabaseService _instance = LocalDatabaseService._internal();
  factory LocalDatabaseService() => _instance;
  LocalDatabaseService._internal();

  late SharedPreferences _prefs;
  bool _isInitialized = false;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    _prefs = await SharedPreferences.getInstance();
    _isInitialized = true;
    print('📱 Local database service initialized');
  }

  // Ensure initialization
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  // Profile Management
  Future<List<UserProfile>> getAllProfiles() async {
    await _ensureInitialized();
    
    final profilesJson = _prefs.getString(_profilesKey);
    if (profilesJson == null) return [];

    try {
      final List<dynamic> profilesList = json.decode(profilesJson);
      return profilesList.map((json) => UserProfile.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error loading profiles: $e');
      return [];
    }
  }

  Future<bool> saveProfile(UserProfile profile) async {
    await _ensureInitialized();
    
    try {
      final profiles = await getAllProfiles();
      
      // Check if profile already exists
      final existingIndex = profiles.indexWhere((p) => p.id == profile.id);
      
      if (existingIndex != -1) {
        // Update existing profile
        profiles[existingIndex] = profile;
      } else {
        // Add new profile
        profiles.add(profile);
      }
      
      final profilesJson = json.encode(profiles.map((p) => p.toJson()).toList());
      final success = await _prefs.setString(_profilesKey, profilesJson);
      
      if (success) {
        print('✅ Profile saved: ${profile.name}');
      }
      
      return success;
    } catch (e) {
      print('❌ Error saving profile: $e');
      return false;
    }
  }

  Future<bool> deleteProfile(String profileId) async {
    await _ensureInitialized();
    
    try {
      final profiles = await getAllProfiles();
      profiles.removeWhere((profile) => profile.id == profileId);
      
      final profilesJson = json.encode(profiles.map((p) => p.toJson()).toList());
      final success = await _prefs.setString(_profilesKey, profilesJson);
      
      // If deleted profile was current, clear current profile
      final currentProfileId = await getCurrentProfileId();
      if (currentProfileId == profileId) {
        await clearCurrentProfile();
      }
      
      if (success) {
        print('✅ Profile deleted: $profileId');
      }
      
      return success;
    } catch (e) {
      print('❌ Error deleting profile: $e');
      return false;
    }
  }

  Future<UserProfile?> getProfileById(String profileId) async {
    await _ensureInitialized();
    
    final profiles = await getAllProfiles();
    try {
      return profiles.firstWhere((profile) => profile.id == profileId);
    } catch (e) {
      return null;
    }
  }

  // Current Profile Management
  Future<bool> setCurrentProfile(String profileId) async {
    await _ensureInitialized();
    
    final success = await _prefs.setString(_currentProfileKey, profileId);
    
    if (success) {
      // Update last used time for the profile
      final profile = await getProfileById(profileId);
      if (profile != null) {
        final updatedProfile = profile.copyWith(lastUsed: DateTime.now());
        await saveProfile(updatedProfile);
      }
      print('✅ Current profile set: $profileId');
    }
    
    return success;
  }

  Future<String?> getCurrentProfileId() async {
    await _ensureInitialized();
    return _prefs.getString(_currentProfileKey);
  }

  Future<UserProfile?> getCurrentProfile() async {
    await _ensureInitialized();
    
    final profileId = await getCurrentProfileId();
    if (profileId == null) return null;
    
    return await getProfileById(profileId);
  }

  Future<bool> clearCurrentProfile() async {
    await _ensureInitialized();
    return await _prefs.remove(_currentProfileKey);
  }

  // Favorites Management
  Future<bool> addToFavorites(String profileId, String itemId, String type) async {
    await _ensureInitialized();
    
    final profile = await getProfileById(profileId);
    if (profile == null) return false;

    UserProfile updatedProfile;
    
    switch (type.toLowerCase()) {
      case 'channel':
      case 'live':
        if (!profile.favoriteChannels.contains(itemId)) {
          updatedProfile = profile.copyWith(
            favoriteChannels: [...profile.favoriteChannels, itemId],
          );
        } else {
          return true; // Already in favorites
        }
        break;
      case 'movie':
      case 'vod':
        if (!profile.favoriteMovies.contains(itemId)) {
          updatedProfile = profile.copyWith(
            favoriteMovies: [...profile.favoriteMovies, itemId],
          );
        } else {
          return true; // Already in favorites
        }
        break;
      case 'series':
        if (!profile.favoriteSeries.contains(itemId)) {
          updatedProfile = profile.copyWith(
            favoriteSeries: [...profile.favoriteSeries, itemId],
          );
        } else {
          return true; // Already in favorites
        }
        break;
      default:
        return false;
    }

    return await saveProfile(updatedProfile);
  }

  Future<bool> removeFromFavorites(String profileId, String itemId, String type) async {
    await _ensureInitialized();
    
    final profile = await getProfileById(profileId);
    if (profile == null) return false;

    UserProfile updatedProfile;
    
    switch (type.toLowerCase()) {
      case 'channel':
      case 'live':
        final updatedChannels = profile.favoriteChannels.where((id) => id != itemId).toList();
        updatedProfile = profile.copyWith(favoriteChannels: updatedChannels);
        break;
      case 'movie':
      case 'vod':
        final updatedMovies = profile.favoriteMovies.where((id) => id != itemId).toList();
        updatedProfile = profile.copyWith(favoriteMovies: updatedMovies);
        break;
      case 'series':
        final updatedSeries = profile.favoriteSeries.where((id) => id != itemId).toList();
        updatedProfile = profile.copyWith(favoriteSeries: updatedSeries);
        break;
      default:
        return false;
    }

    return await saveProfile(updatedProfile);
  }

  Future<bool> isFavorite(String profileId, String itemId, String type) async {
    await _ensureInitialized();
    
    final profile = await getProfileById(profileId);
    if (profile == null) return false;

    switch (type.toLowerCase()) {
      case 'channel':
      case 'live':
        return profile.favoriteChannels.contains(itemId);
      case 'movie':
      case 'vod':
        return profile.favoriteMovies.contains(itemId);
      case 'series':
        return profile.favoriteSeries.contains(itemId);
      default:
        return false;
    }
  }

  // Continue Watching Management
  Future<bool> addToContinueWatching(String profileId, ContinueWatchingItem item) async {
    await _ensureInitialized();
    
    final profile = await getProfileById(profileId);
    if (profile == null) return false;

    final continueWatching = [...profile.continueWatching];
    
    // Remove existing item with same ID if exists
    continueWatching.removeWhere((existing) => existing.id == item.id);
    
    // Add new item at the beginning
    continueWatching.insert(0, item);
    
    // Keep only last 20 items
    if (continueWatching.length > 20) {
      continueWatching.removeRange(20, continueWatching.length);
    }

    final updatedProfile = profile.copyWith(continueWatching: continueWatching);
    return await saveProfile(updatedProfile);
  }

  Future<bool> removeFromContinueWatching(String profileId, String itemId) async {
    await _ensureInitialized();
    
    final profile = await getProfileById(profileId);
    if (profile == null) return false;

    final updatedContinueWatching = profile.continueWatching
        .where((item) => item.id != itemId)
        .toList();

    final updatedProfile = profile.copyWith(continueWatching: updatedContinueWatching);
    return await saveProfile(updatedProfile);
  }

  // Login Credentials (optional - for remember me functionality)
  Future<bool> saveLastLoginCredentials(String username, String password) async {
    await _ensureInitialized();
    
    final credentials = {
      'username': username,
      'password': password,
      'savedAt': DateTime.now().toIso8601String(),
    };
    
    return await _prefs.setString(_lastLoginKey, json.encode(credentials));
  }

  Future<Map<String, String>?> getLastLoginCredentials() async {
    await _ensureInitialized();
    
    final credentialsJson = _prefs.getString(_lastLoginKey);
    if (credentialsJson == null) return null;

    try {
      final Map<String, dynamic> credentials = json.decode(credentialsJson);
      return {
        'username': credentials['username'] ?? '',
        'password': credentials['password'] ?? '',
      };
    } catch (e) {
      print('❌ Error loading login credentials: $e');
      return null;
    }
  }

  Future<bool> clearLastLoginCredentials() async {
    await _ensureInitialized();
    return await _prefs.remove(_lastLoginKey);
  }

  // Save current login session
  Future<void> saveLoginCredentials(String username, String password) async {
    await _ensureInitialized();
    await _prefs.setString('current_username', username);
    await _prefs.setString('current_password', password);
    await _prefs.setBool('is_logged_in', true);
    print('💾 Current login session saved for user: $username');
  }

  // Get current login session
  Future<Map<String, String>?> getSavedLoginCredentials() async {
    await _ensureInitialized();
    final username = _prefs.getString('current_username');
    final password = _prefs.getString('current_password');
    final isLoggedIn = _prefs.getBool('is_logged_in') ?? false;

    if (isLoggedIn && username != null && password != null) {
      return {'username': username, 'password': password};
    }
    return null;
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    await _ensureInitialized();
    return _prefs.getBool('is_logged_in') ?? false;
  }

  // Clear current login session (logout)
  Future<void> clearLoginCredentials() async {
    await _ensureInitialized();
    await _prefs.remove('current_username');
    await _prefs.remove('current_password');
    await _prefs.setBool('is_logged_in', false);
    print('🚪 Current login session cleared');
  }

  // Clear all data
  Future<bool> clearAllData() async {
    await _ensureInitialized();
    
    try {
      await _prefs.remove(_profilesKey);
      await _prefs.remove(_currentProfileKey);
      await _prefs.remove(_lastLoginKey);
      print('✅ All local data cleared');
      return true;
    } catch (e) {
      print('❌ Error clearing data: $e');
      return false;
    }
  }
}
