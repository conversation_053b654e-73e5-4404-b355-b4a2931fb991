{"logs": [{"outputFile": "com.example.iptv_player.app-mergeDebugResources-43:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3390,3487,3589,3688,3788,3891,4004,8819", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3482,3584,3683,3783,3886,3999,4115,8915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,76,90,91,94,93,100,92,94,93,90,90,82,103,107,100,104,114,104,156,98,83", "endOffsets": "207,309,418,502,605,724,802,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1899,2003,2111,2212,2317,2432,2537,2694,2793,2877"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "697,804,906,1015,1099,1202,1321,1399,1476,1567,1659,1754,1848,1949,2042,2137,2231,2322,2413,2496,2600,2708,2809,2914,3029,3134,3291,8735", "endColumns": "106,101,108,83,102,118,77,76,90,91,94,93,100,92,94,93,90,90,82,103,107,100,104,114,104,156,98,83", "endOffsets": "799,901,1010,1094,1197,1316,1394,1471,1562,1654,1749,1843,1944,2037,2132,2226,2317,2408,2491,2595,2703,2804,2909,3024,3129,3286,3385,8814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,264,339,482,651,731", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "172,259,334,477,646,726,803"}, "to": {"startLines": "53,105,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4120,8094,8517,8592,8920,9089,9169", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "4187,8176,8587,8730,9084,9164,9241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "54,106,107,108", "startColumns": "4,4,4,4", "startOffsets": "4192,8181,8292,8406", "endColumns": "116,110,113,110", "endOffsets": "4304,8287,8401,8512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,259,328,419,489,579,667", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "123,186,254,323,414,484,574,662,742"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6329,6402,6465,6533,6602,6693,6763,6853,6941", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "6397,6460,6528,6597,6688,6758,6848,6936,7016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\058b46d59488c77d02cd8814b15f89a0\\transformed\\jetified-media3-ui-1.4.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,473,647,723,798,870,973,1074,1153,1221,1320,1421,1489,1552,1615,1683,1813,1933,2060,2128,2206,2276,2361,2446,2530,2593,2667,2720,2781,2831,2892,2954,3020,3084,3149,3210,3269,3338,3396,3456,3530,3604,3667", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,57,59,73,73,62,72", "endOffsets": "285,468,642,718,793,865,968,1069,1148,1216,1315,1416,1484,1547,1610,1678,1808,1928,2055,2123,2201,2271,2356,2441,2525,2588,2662,2715,2776,2826,2887,2949,3015,3079,3144,3205,3264,3333,3391,3451,3525,3599,3662,3735"}, "to": {"startLines": "2,11,15,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,340,523,4309,4385,4460,4532,4635,4736,4815,4883,4982,5083,5151,5214,5277,5345,5475,5595,5722,5790,5868,5938,6023,6108,6192,6255,7021,7074,7135,7185,7246,7308,7374,7438,7503,7564,7623,7692,7750,7810,7884,7958,8021", "endLines": "10,14,18,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,57,59,73,73,62,72", "endOffsets": "335,518,692,4380,4455,4527,4630,4731,4810,4878,4977,5078,5146,5209,5272,5340,5470,5590,5717,5785,5863,5933,6018,6103,6187,6250,6324,7069,7130,7180,7241,7303,7369,7433,7498,7559,7618,7687,7745,7805,7879,7953,8016,8089"}}]}]}