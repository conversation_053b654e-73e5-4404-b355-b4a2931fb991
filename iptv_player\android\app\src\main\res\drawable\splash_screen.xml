<?xml version="1.0" encoding="utf-8"?>
<!-- Simple splash with gradient logo that can be sized -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Background: Color(0xFF1A1A2E) -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#1A1A2E" />
        </shape>
    </item>

    <!-- Logo: 120x120 gradient circle (fallback from Flutter) -->
    <item android:gravity="center">
        <shape android:shape="oval">
            <gradient
                android:startColor="#FFB347"
                android:endColor="#FF6B35"
                android:angle="135" />
            <size android:width="120dp" android:height="120dp" />
        </shape>
    </item>

    <!-- GO text -->
    <item android:gravity="center">
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFF" />
            <corners android:radius="8dp" />
            <size android:width="50dp" android:height="25dp" />
        </shape>
    </item>
</layer-list>
