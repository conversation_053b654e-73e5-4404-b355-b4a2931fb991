<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res"><file name="launch_background" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="launch_logo" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\drawable\launch_logo.xml" qualifiers="" type="drawable"/><file name="launch_logo_vector" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\drawable\launch_logo_vector.xml" qualifiers="" type="drawable"/><file name="loading_dots" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\drawable\loading_dots.xml" qualifiers="" type="drawable"/><file name="logo" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\drawable\logo.png" qualifiers="" type="drawable"/><file name="logo_small" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\drawable\logo_small.xml" qualifiers="" type="drawable"/><file name="logo_splash" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\drawable\logo_splash.xml" qualifiers="" type="drawable"/><file name="splash_screen" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\drawable\splash_screen.xml" qualifiers="" type="drawable"/><file name="launch_background" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-hdpi\logo.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-mdpi\logo.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-xhdpi\logo.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-xxhdpi\logo.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\mipmap-xxxhdpi\logo.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/splash_screen</item>
        <item name="android:windowFullscreen">true</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">#FF1A1A2E</item>
        <item name="android:windowFullscreen">true</item>
    </style></file><file path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/splash_screen</item>
        <item name="android:windowFullscreen">true</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">#FF1A1A2E</item>
        <item name="android:windowFullscreen">true</item>
    </style></file><file name="network_security_config" path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>