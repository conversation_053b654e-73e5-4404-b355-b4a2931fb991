import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../models/xtreme_api_models.dart';
import '../services/xtreme_api_service.dart';

class VideoPlayerScreen extends StatefulWidget {
  final VodContent movie;

  const VideoPlayerScreen({
    Key? key,
    required this.movie,
  }) : super(key: key);

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  final XtremeApiService _apiService = XtremeApiService();
  
  VideoPlayerController? _controller;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  bool _showControls = true;
  bool _isPlaying = false;
  
  // Control visibility timer
  DateTime _lastInteraction = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _keepScreenOn();
    _hideControlsAfterDelay();
  }

  @override
  void dispose() {
    _controller?.dispose();
    WakelockPlus.disable();
    super.dispose();
  }

  Future<void> _keepScreenOn() async {
    await WakelockPlus.enable();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Get the video stream URL
      final streamUrl = _apiService.getVodStreamUrl(
        widget.movie.streamId,
        widget.movie.containerExtension,
      );

      print('🎬 Initializing video player for: ${widget.movie.name}');
      print('📺 Stream URL: $streamUrl');

      _controller = VideoPlayerController.networkUrl(
        Uri.parse(streamUrl),
        videoPlayerOptions: VideoPlayerOptions(
          allowBackgroundPlayback: true,
          mixWithOthers: false,
        ),
      );

      await _controller!.initialize();
      
      setState(() {
        _isLoading = false;
        _isPlaying = true;
      });

      // Auto-play the video
      _controller!.play();
      
      // Listen to player state changes
      _controller!.addListener(_onPlayerStateChanged);

      print('✅ Video player initialized successfully');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = _getErrorMessage(e.toString());
      });
      print('❌ Error initializing video player: $e');
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('MediaCodecVideoRenderer')) {
      return 'Video format not supported on this device. The movie may use a codec that is not compatible with your Android TV.';
    } else if (error.contains('Source error')) {
      return 'Unable to load video stream. Please check your internet connection and try again.';
    } else if (error.contains('Cleartext')) {
      return 'Network security error. Please try again.';
    } else {
      return 'Unable to play video. Please try a different movie or check your connection.';
    }
  }

  void _onPlayerStateChanged() {
    if (_controller != null) {
      setState(() {
        _isPlaying = _controller!.value.isPlaying;
      });
    }
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && DateTime.now().difference(_lastInteraction).inSeconds >= 3) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
      _lastInteraction = DateTime.now();
    });
    _hideControlsAfterDelay();
  }

  void _togglePlayPause() {
    if (_controller != null) {
      if (_controller!.value.isPlaying) {
        _controller!.pause();
      } else {
        _controller!.play();
      }
      _showControlsTemporarily();
    }
  }

  void _seekForward() {
    if (_controller != null) {
      final currentPosition = _controller!.value.position;
      final newPosition = currentPosition + const Duration(seconds: 10);
      final duration = _controller!.value.duration;
      
      if (newPosition < duration) {
        _controller!.seekTo(newPosition);
      }
      _showControlsTemporarily();
    }
  }

  void _seekBackward() {
    if (_controller != null) {
      final currentPosition = _controller!.value.position;
      final newPosition = currentPosition - const Duration(seconds: 10);
      
      if (newPosition > Duration.zero) {
        _controller!.seekTo(newPosition);
      } else {
        _controller!.seekTo(Duration.zero);
      }
      _showControlsTemporarily();
    }
  }

  void _goBack() {
    Navigator.of(context).pop();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            _showControlsTemporarily();
            
            // Handle remote control keys
            if (event.logicalKey == LogicalKeyboardKey.select ||
                event.logicalKey == LogicalKeyboardKey.enter ||
                event.logicalKey == LogicalKeyboardKey.space) {
              _togglePlayPause();
              return KeyEventResult.handled;
            }
            
            if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
              _seekForward();
              return KeyEventResult.handled;
            }
            
            if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
              _seekBackward();
              return KeyEventResult.handled;
            }
            
            if (event.logicalKey == LogicalKeyboardKey.goBack ||
                event.logicalKey == LogicalKeyboardKey.escape) {
              _goBack();
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: GestureDetector(
          onTap: _showControlsTemporarily,
          child: Stack(
            children: [
              // Video player
              _buildVideoPlayer(),
              
              // Controls overlay
              if (_showControls) _buildControlsOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            ),
            SizedBox(height: 20),
            Text(
              'Loading video...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 20),
            const Text(
              'Failed to load video',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              _errorMessage ?? 'Unknown error',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _initializePlayer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFE17055),
                  ),
                  child: const Text(
                    'Retry',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 20),
                ElevatedButton(
                  onPressed: _goBack,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                  ),
                  child: const Text(
                    'Go Back',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    if (_controller != null && _controller!.value.isInitialized) {
      return Center(
        child: AspectRatio(
          aspectRatio: _controller!.value.aspectRatio,
          child: VideoPlayer(_controller!),
        ),
      );
    }

    return const Center(
      child: Text(
        'Video not available',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
      ),
    );
  }

  Widget _buildControlsOverlay() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          // Top controls
          _buildTopControls(),
          
          const Spacer(),
          
          // Center play/pause button
          _buildCenterControls(),
          
          const Spacer(),
          
          // Bottom controls
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: _goBack,
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 32,
            ),
          ),
          
          const SizedBox(width: 20),
          
          // Movie title
          Expanded(
            child: Text(
              widget.movie.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Seek backward
        IconButton(
          onPressed: _seekBackward,
          icon: const Icon(
            Icons.replay_10,
            color: Colors.white,
            size: 48,
          ),
        ),
        
        const SizedBox(width: 40),
        
        // Play/Pause
        IconButton(
          onPressed: _togglePlayPause,
          icon: Icon(
            _isPlaying ? Icons.pause : Icons.play_arrow,
            color: Colors.white,
            size: 64,
          ),
        ),
        
        const SizedBox(width: 40),
        
        // Seek forward
        IconButton(
          onPressed: _seekForward,
          icon: const Icon(
            Icons.forward_10,
            color: Colors.white,
            size: 48,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomControls() {
    if (_controller == null || !_controller!.value.isInitialized) {
      return const SizedBox.shrink();
    }

    final position = _controller!.value.position;
    final duration = _controller!.value.duration;
    final progress = duration.inMilliseconds > 0 
        ? position.inMilliseconds / duration.inMilliseconds 
        : 0.0;

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Progress bar
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withOpacity(0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            minHeight: 4,
          ),
          
          const SizedBox(height: 10),
          
          // Time indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(position),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              Text(
                _formatDuration(duration),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
