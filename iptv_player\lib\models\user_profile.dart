class UserProfile {
  final String id;
  final String name;
  final String avatarPath;
  final bool isKidsProfile;
  final DateTime createdAt;
  final DateTime lastUsed;
  final List<String> favoriteChannels;
  final List<String> favoriteMovies;
  final List<String> favoriteSeries;
  final List<ContinueWatchingItem> continueWatching;

  UserProfile({
    required this.id,
    required this.name,
    required this.avatarPath,
    this.isKidsProfile = false,
    required this.createdAt,
    required this.lastUsed,
    this.favoriteChannels = const [],
    this.favoriteMovies = const [],
    this.favoriteSeries = const [],
    this.continueWatching = const [],
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      avatarPath: json['avatarPath'] ?? '',
      isKidsProfile: json['isKidsProfile'] ?? false,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastUsed: DateTime.parse(json['lastUsed'] ?? DateTime.now().toIso8601String()),
      favoriteChannels: List<String>.from(json['favoriteChannels'] ?? []),
      favoriteMovies: List<String>.from(json['favoriteMovies'] ?? []),
      favoriteSeries: List<String>.from(json['favoriteSeries'] ?? []),
      continueWatching: (json['continueWatching'] as List<dynamic>?)
          ?.map((item) => ContinueWatchingItem.fromJson(item))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'avatarPath': avatarPath,
      'isKidsProfile': isKidsProfile,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
      'favoriteChannels': favoriteChannels,
      'favoriteMovies': favoriteMovies,
      'favoriteSeries': favoriteSeries,
      'continueWatching': continueWatching.map((item) => item.toJson()).toList(),
    };
  }

  UserProfile copyWith({
    String? id,
    String? name,
    String? avatarPath,
    bool? isKidsProfile,
    DateTime? createdAt,
    DateTime? lastUsed,
    List<String>? favoriteChannels,
    List<String>? favoriteMovies,
    List<String>? favoriteSeries,
    List<ContinueWatchingItem>? continueWatching,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      avatarPath: avatarPath ?? this.avatarPath,
      isKidsProfile: isKidsProfile ?? this.isKidsProfile,
      createdAt: createdAt ?? this.createdAt,
      lastUsed: lastUsed ?? this.lastUsed,
      favoriteChannels: favoriteChannels ?? this.favoriteChannels,
      favoriteMovies: favoriteMovies ?? this.favoriteMovies,
      favoriteSeries: favoriteSeries ?? this.favoriteSeries,
      continueWatching: continueWatching ?? this.continueWatching,
    );
  }
}

class ContinueWatchingItem {
  final String id;
  final String title;
  final String type; // 'live', 'movie', 'series'
  final String streamId;
  final String thumbnailUrl;
  final int watchedDuration; // in seconds
  final int totalDuration; // in seconds
  final DateTime lastWatched;
  final String? seasonNumber;
  final String? episodeNumber;

  ContinueWatchingItem({
    required this.id,
    required this.title,
    required this.type,
    required this.streamId,
    required this.thumbnailUrl,
    required this.watchedDuration,
    required this.totalDuration,
    required this.lastWatched,
    this.seasonNumber,
    this.episodeNumber,
  });

  factory ContinueWatchingItem.fromJson(Map<String, dynamic> json) {
    return ContinueWatchingItem(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      type: json['type'] ?? '',
      streamId: json['streamId'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? '',
      watchedDuration: json['watchedDuration'] ?? 0,
      totalDuration: json['totalDuration'] ?? 0,
      lastWatched: DateTime.parse(json['lastWatched'] ?? DateTime.now().toIso8601String()),
      seasonNumber: json['seasonNumber'],
      episodeNumber: json['episodeNumber'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'streamId': streamId,
      'thumbnailUrl': thumbnailUrl,
      'watchedDuration': watchedDuration,
      'totalDuration': totalDuration,
      'lastWatched': lastWatched.toIso8601String(),
      'seasonNumber': seasonNumber,
      'episodeNumber': episodeNumber,
    };
  }

  double get progressPercentage {
    if (totalDuration == 0) return 0.0;
    return (watchedDuration / totalDuration).clamp(0.0, 1.0);
  }

  String get progressText {
    final watched = Duration(seconds: watchedDuration);
    final total = Duration(seconds: totalDuration);
    
    String formatDuration(Duration duration) {
      String twoDigits(int n) => n.toString().padLeft(2, "0");
      String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
      String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
      
      if (duration.inHours > 0) {
        return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
      } else {
        return "$twoDigitMinutes:$twoDigitSeconds";
      }
    }
    
    return "${formatDuration(watched)} / ${formatDuration(total)}";
  }
}

// Predefined avatar options
class AvatarOption {
  final String id;
  final String name;
  final String assetPath;
  final bool isKidsAvatar;

  const AvatarOption({
    required this.id,
    required this.name,
    required this.assetPath,
    this.isKidsAvatar = false,
  });
}

class DefaultAvatars {
  static const List<AvatarOption> avatars = [
    // Adult avatars
    AvatarOption(
      id: 'avatar_1',
      name: 'Blue',
      assetPath: 'assets/avatars/avatar_1.png',
    ),
    AvatarOption(
      id: 'avatar_2',
      name: 'Green',
      assetPath: 'assets/avatars/avatar_2.png',
    ),
    AvatarOption(
      id: 'avatar_3',
      name: 'Purple',
      assetPath: 'assets/avatars/avatar_3.png',
    ),
    AvatarOption(
      id: 'avatar_4',
      name: 'Orange',
      assetPath: 'assets/avatars/avatar_4.png',
    ),
    AvatarOption(
      id: 'avatar_5',
      name: 'Red',
      assetPath: 'assets/avatars/avatar_5.png',
    ),
    
    // Kids avatars
    AvatarOption(
      id: 'kids_1',
      name: 'Bear',
      assetPath: 'assets/avatars/kids_1.png',
      isKidsAvatar: true,
    ),
    AvatarOption(
      id: 'kids_2',
      name: 'Cat',
      assetPath: 'assets/avatars/kids_2.png',
      isKidsAvatar: true,
    ),
    AvatarOption(
      id: 'kids_3',
      name: 'Dog',
      assetPath: 'assets/avatars/kids_3.png',
      isKidsAvatar: true,
    ),
  ];

  static AvatarOption getAvatarById(String id) {
    return avatars.firstWhere(
      (avatar) => avatar.id == id,
      orElse: () => avatars.first,
    );
  }

  static List<AvatarOption> getAdultAvatars() {
    return avatars.where((avatar) => !avatar.isKidsAvatar).toList();
  }

  static List<AvatarOption> getKidsAvatars() {
    return avatars.where((avatar) => avatar.isKidsAvatar).toList();
  }
}
