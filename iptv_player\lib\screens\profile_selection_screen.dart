import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/user_profile.dart';
import '../services/local_database_service.dart';
import '../widgets/directional_focus_widget.dart';
import 'add_profile_screen.dart';
import 'goplay_home_screen.dart';
import 'manage_profiles_screen.dart';

class ProfileSelectionScreen extends StatefulWidget {
  const ProfileSelectionScreen({Key? key}) : super(key: key);

  @override
  State<ProfileSelectionScreen> createState() => _ProfileSelectionScreenState();
}

class _ProfileSelectionScreenState extends State<ProfileSelectionScreen>
    with TickerProviderStateMixin {
  final LocalDatabaseService _dbService = LocalDatabaseService();
  List<UserProfile> _profiles = [];
  bool _isLoading = true;
  
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _loadProfiles();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _loadProfiles() async {
    try {
      await _dbService.initialize();
      final profiles = await _dbService.getAllProfiles();
      
      setState(() {
        _profiles = profiles;
        _isLoading = false;
      });
      
      _fadeController.forward();
    } catch (e) {
      print('❌ Error loading profiles: $e');
      setState(() {
        _isLoading = false;
      });
      _fadeController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: _buildContent(),
          );
        },
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Color(0xFFFF6B35),
        ),
      );
    }

    return Center(
      child: SingleChildScrollView(
        child: Container(
          width: 600,
          padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              _buildHeader(),

              const SizedBox(height: 30),

              // Profiles grid
              _buildProfilesGrid(),

              const SizedBox(height: 25),

              // Add profile button
              _buildAddProfileButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Title
        const Text(
          'Who\'s watching?',
          style: TextStyle(
            color: Colors.white,
            fontSize: 32,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),

        // Subtitle
        Text(
          'Select your profile to continue',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildProfilesGrid() {
    if (_profiles.isEmpty) {
      return _buildEmptyState();
    }

    // Calculate grid layout
    final maxProfilesPerRow = 4; // Fixed 4 profiles per row
    final profilesWithAdd = _profiles.length + 1; // +1 for add button
    final rows = (profilesWithAdd / maxProfilesPerRow).ceil();

    return Column(
      children: List.generate(rows, (rowIndex) {
        final startIndex = rowIndex * maxProfilesPerRow;
        final endIndex = (startIndex + maxProfilesPerRow).clamp(0, _profiles.length);
        final rowProfiles = _profiles.sublist(startIndex, endIndex);

        return Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ...rowProfiles.asMap().entries.map((entry) {
                final index = startIndex + entry.key;
                final profile = entry.value;
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: _buildProfileCard(profile, index == 0),
                );
              }),

              // Add "Add Profile" button in the last row if there's space
              if (rowIndex == rows - 1 && rowProfiles.length < maxProfilesPerRow) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: _buildAddProfileCard(),
                ),

                // Add "Manage Profiles" button if there are profiles and space
                // if (profiles.isNotEmpty && rowProfiles.length < maxProfilesPerRow - 1)
                //   Padding(
                //     padding: const EdgeInsets.symmetric(horizontal: 10),
                //     child: _buildManageProfileCard(),
                //   ),
              ],
            ],
          ),
        );
      }),
    );
  }

  Widget _buildEmptyState() {
    return Column(
      children: [
        Icon(
          Icons.people_outline,
          size: 80,
          color: Colors.white.withOpacity(0.5),
        ),
        const SizedBox(height: 20),
        Text(
          'No profiles yet',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 18,
          ),
        ),
        const SizedBox(height: 10),
        Text(
          'Create your first profile to get started',
          style: TextStyle(
            color: Colors.white.withOpacity(0.5),
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildProfileCard(UserProfile profile, bool autofocus) {
    // Check if avatarPath is an emoji (length 1-4 characters for emojis) or old system
    final isEmoji = profile.avatarPath.length <= 4 &&
                   !profile.avatarPath.contains('avatar');

    return Container(
      width: 120,
      child: Column(
        children: [
          // Avatar with focus border
          DirectionalFocusWidget(
            autofocus: autofocus,
            borderRadius: BorderRadius.circular(40), // Circular focus
            onPressed: () => _selectProfile(profile),
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: profile.isKidsProfile
                      ? [const Color(0xFF4CAF50), const Color(0xFF66BB6A)]
                      : [const Color(0xFF2196F3), const Color(0xFF42A5F5)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle, // Make it circular
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  isEmoji
                      ? profile.avatarPath
                      : (profile.name.isNotEmpty ? profile.name[0].toUpperCase() : '?'),
                  style: TextStyle(
                    fontSize: isEmoji ? 36 : 28,
                    color: isEmoji ? null : Colors.white,
                    fontWeight: isEmoji ? null : FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),

          // Name (no focus border)
          Text(
            profile.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          // Kids badge
          if (profile.isKidsProfile) ...[
            const SizedBox(height: 3),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Text(
                'KIDS',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 8,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAddProfileCard() {
    return Container(
      width: 120,
      child: Column(
        children: [
          // Add button with focus border
          DirectionalFocusWidget(
            borderRadius: BorderRadius.circular(40), // Circular focus
            onPressed: _addNewProfile,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(0xFF3D3D5C),
                shape: BoxShape.circle, // Make it circular
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 2,
                  style: BorderStyle.solid,
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),

          // Label (no focus border)
          const Text(
            'Add Profile',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAddProfileButton() {
    if (_profiles.isNotEmpty) return const SizedBox.shrink();
    
    return DirectionalFocusWidget(
      autofocus: _profiles.isEmpty,
      borderRadius: BorderRadius.circular(12),
      onPressed: _addNewProfile,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFFFB347), Color(0xFFFF6B35)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'Create Your First Profile',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _selectProfile(UserProfile profile) async {
    // Set as current profile
    await _dbService.setCurrentProfile(profile.id);
    
    // Navigate to main app
    if (mounted) {
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const GoPlayHomeScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 600),
        ),
      );
    }
  }

  Widget _buildManageProfileCard() {
    return Container(
      width: 120,
      child: Column(
        children: [
          // Manage button with focus border
          DirectionalFocusWidget(
            borderRadius: BorderRadius.circular(40), // Circular focus
            onPressed: _manageProfiles,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFF6B35), Color(0xFFFFB347)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle, // Make it circular
              ),
              child: const Center(
                child: Icon(
                  Icons.settings,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),

          // Label
          const Text(
            'Manage',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _manageProfiles() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ManageProfilesScreen(),
      ),
    );
  }

  void _addNewProfile() async {
    final result = await Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const AddProfileScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );

    // Reload profiles if a new one was added
    if (result == true) {
      _loadProfiles();
    }
  }
}
