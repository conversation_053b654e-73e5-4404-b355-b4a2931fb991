#Fri Jul 18 20:56:38 EET 2025
com.example.iptv_player.app-main-1\:/color/button_tint_selector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\button_tint_selector.xml
com.example.iptv_player.app-main-1\:/drawable-v21/launch_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-v21\\launch_background.xml
com.example.iptv_player.app-main-1\:/drawable/button_selector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_selector.xml
com.example.iptv_player.app-main-1\:/drawable/gradient_bottom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_bottom.xml
com.example.iptv_player.app-main-1\:/drawable/gradient_top.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_top.xml
com.example.iptv_player.app-main-1\:/drawable/ic_arrow_back.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_back.xml
com.example.iptv_player.app-main-1\:/drawable/ic_forward_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_forward_10.xml
com.example.iptv_player.app-main-1\:/drawable/ic_replay_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_replay_10.xml
com.example.iptv_player.app-main-1\:/drawable/ic_settings.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.xml
com.example.iptv_player.app-main-1\:/drawable/launch_logo.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\launch_logo.xml
com.example.iptv_player.app-main-1\:/drawable/launch_logo_vector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\launch_logo_vector.xml
com.example.iptv_player.app-main-1\:/drawable/loading_dots.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\loading_dots.xml
com.example.iptv_player.app-main-1\:/drawable/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logo.png
com.example.iptv_player.app-main-1\:/drawable/logo_small.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logo_small.xml
com.example.iptv_player.app-main-1\:/drawable/logo_splash.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logo_splash.xml
com.example.iptv_player.app-main-1\:/drawable/splash_screen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\splash_screen.xml
com.example.iptv_player.app-main-1\:/layout/activity_exoplayer.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_exoplayer.xml
com.example.iptv_player.app-main-1\:/layout/custom_player_control_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\custom_player_control_view.xml
com.example.iptv_player.app-main-1\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-1\:/mipmap-hdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\logo.png
com.example.iptv_player.app-main-1\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-1\:/mipmap-mdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\logo.png
com.example.iptv_player.app-main-1\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-1\:/mipmap-xhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\logo.png
com.example.iptv_player.app-main-1\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-1\:/mipmap-xxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\logo.png
com.example.iptv_player.app-main-1\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-1\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\logo.png
com.example.iptv_player.app-main-1\:/xml/network_security_config.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\network_security_config.xml
