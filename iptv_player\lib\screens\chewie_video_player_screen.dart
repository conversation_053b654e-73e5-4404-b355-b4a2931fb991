import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../models/xtreme_api_models.dart';
import '../services/xtreme_api_service.dart';

class ChewieVideoPlayerScreen extends StatefulWidget {
  final VodContent movie;

  const ChewieVideoPlayerScreen({
    Key? key,
    required this.movie,
  }) : super(key: key);

  @override
  State<ChewieVideoPlayerScreen> createState() => _ChewieVideoPlayerScreenState();
}

class _ChewieVideoPlayerScreenState extends State<ChewieVideoPlayerScreen> {
  final XtremeApiService _apiService = XtremeApiService();
  
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _keepScreenOn();
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoPlayerController?.dispose();
    WakelockPlus.disable();
    super.dispose();
  }

  Future<void> _keepScreenOn() async {
    await WakelockPlus.enable();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Get the video stream URL
      final streamUrl = _apiService.getVodStreamUrl(
        widget.movie.streamId,
        widget.movie.containerExtension,
      );

      print('🎬 Initializing Chewie Player for: ${widget.movie.name}');
      print('📺 Stream URL: $streamUrl');

      // Initialize video player controller with IPTV optimizations
      _videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(streamUrl),
        videoPlayerOptions: VideoPlayerOptions(
          allowBackgroundPlayback: true,
          mixWithOthers: false,
          webOptions: VideoPlayerWebOptions(
            controls: VideoPlayerWebOptionsControls.disabled(),
          ),
        ),
        httpHeaders: {
          'User-Agent': 'ExoPlayerLib/2.18.1 (Linux; Android 10; Android TV) ExoPlayerLib/2.18.1',
          'Accept': '*/*',
          'Accept-Encoding': 'identity',
          'Connection': 'keep-alive',
          'Cache-Control': 'no-cache',
        },
      );

      await _videoPlayerController!.initialize();

      // Initialize Chewie controller with Android TV optimized settings
      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: true,
        looping: false,
        allowFullScreen: false, // We're already fullscreen
        allowMuting: true,
        allowPlaybackSpeedChanging: false,
        showControlsOnInitialize: true,
        hideControlsTimer: const Duration(seconds: 4),
        isLive: false,
        showOptions: false,
        optionsTranslation: OptionsTranslation(
          playbackSpeedButtonText: 'Speed',
          subtitlesButtonText: 'Subtitles',
          cancelButtonText: 'Cancel',
        ),
        materialProgressColors: ChewieProgressColors(
          playedColor: const Color(0xFFE17055),
          handleColor: const Color(0xFFE17055),
          bufferedColor: Colors.white38,
          backgroundColor: Colors.white24,
        ),
        placeholder: Container(
          color: Colors.black,
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            ),
          ),
        ),
        autoInitialize: true,
        errorBuilder: (context, errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 64,
                ),
                const SizedBox(height: 20),
                const Text(
                  'Video Error',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  child: Text(
                    errorMessage,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          );
        },
      );

      setState(() {
        _isLoading = false;
      });

      print('✅ Chewie Player initialized successfully');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = _getErrorMessage(e.toString());
      });
      print('❌ Error initializing Chewie Player: $e');
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('MediaCodecVideoRenderer')) {
      return 'Video format not supported on this device. The movie may use a codec that is not compatible with your Android TV.';
    } else if (error.contains('Source error')) {
      return 'Unable to load video stream. Please check your internet connection and try again.';
    } else if (error.contains('Cleartext')) {
      return 'Network security error. Please try again.';
    } else {
      return 'Unable to play video. Please try a different movie or check your connection.';
    }
  }

  void _goBack() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            // Handle back button
            if (event.logicalKey == LogicalKeyboardKey.goBack ||
                event.logicalKey == LogicalKeyboardKey.escape) {
              _goBack();
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: Stack(
          children: [
            // Video player
            _buildVideoPlayer(),
            
            // Back button overlay (only show when there's an error)
            if (_hasError) _buildBackButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            ),
            SizedBox(height: 20),
            Text(
              'Loading video...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 20),
            const Text(
              'Failed to load video',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                _errorMessage ?? 'Unknown error',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _initializePlayer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFE17055),
                  ),
                  child: const Text(
                    'Retry',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 20),
                ElevatedButton(
                  onPressed: _goBack,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                  ),
                  child: const Text(
                    'Go Back',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    if (_chewieController != null) {
      return Center(
        child: Chewie(
          controller: _chewieController!,
        ),
      );
    }

    return const Center(
      child: Text(
        'Video not available',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return Positioned(
      top: 40,
      left: 40,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(25),
        ),
        child: IconButton(
          onPressed: _goBack,
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }
}
