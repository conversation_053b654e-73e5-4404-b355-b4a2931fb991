import 'package:flutter/material.dart';
import '../services/local_database_service.dart';
import '../services/xtreme_api_service.dart';
import 'simple_login_screen.dart';
import 'profile_selection_screen.dart';

class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  final LocalDatabaseService _dbService = LocalDatabaseService();
  final XtremeApiService _apiService = XtremeApiService();

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Give the native launch screen time to show
      await Future.delayed(const Duration(milliseconds: 2000));
      
      print('🚀 Initializing app...');
      
      // Check for saved credentials
      final credentials = await _dbService.getSavedLoginCredentials();
      
      if (credentials != null) {
        print('🔑 Found saved credentials, attempting auto-login...');
        
        // Try to authenticate with saved credentials
        final success = await _apiService.authenticate(
          credentials['username']!,
          credentials['password']!,
        );
        
        if (success) {
          print('✅ Authentication successful for user: ${credentials['username']}');
          
          // Check if user has profiles
          final profiles = await _dbService.getAllProfiles();
          
          if (profiles.isNotEmpty) {
            print('👥 Found ${profiles.length} profiles, going to profile selection');
            _navigateToProfileSelection();
          } else {
            print('👤 No profiles found, going to profile selection to create one');
            _navigateToProfileSelection();
          }
        } else {
          print('❌ Authentication failed, going to login');
          _navigateToLogin();
        }
      } else {
        print('🔐 No saved credentials, going to login');
        _navigateToLogin();
      }
    } catch (e) {
      print('❌ Error during app initialization: $e');
      _navigateToLogin();
    }
  }

  void _navigateToLogin() {
    if (mounted) {
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const SimpleLoginScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  void _navigateToProfileSelection() {
    if (mounted) {
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const ProfileSelectionScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show the same splash content to avoid empty screen
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo matching the native splash
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFB347), Color(0xFFFF6B35)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
              ),
              child: const Center(
                child: Text(
                  'GO',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 40),
            // Loading indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
