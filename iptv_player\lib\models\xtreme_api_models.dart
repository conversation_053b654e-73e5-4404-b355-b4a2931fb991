// Xtreme Player API Models
class XtremeAuthResponse {
  final UserInfo userInfo;
  final ServerInfo serverInfo;

  XtremeAuthResponse({
    required this.userInfo,
    required this.serverInfo,
  });

  factory XtremeAuthResponse.fromJson(Map<String, dynamic> json) {
    return XtremeAuthResponse(
      userInfo: UserInfo.fromJson(json['user_info']),
      serverInfo: ServerInfo.fromJson(json['server_info']),
    );
  }
}

class UserInfo {
  final String username;
  final String password;
  final String message;
  final String auth;
  final String status;
  final String expDate;
  final bool isTrial;
  final int activeCons;
  final String createdAt;
  final int maxConnections;
  final List<String> allowedOutputFormats;

  UserInfo({
    required this.username,
    required this.password,
    required this.message,
    required this.auth,
    required this.status,
    required this.expDate,
    required this.isTrial,
    required this.activeCons,
    required this.createdAt,
    required this.maxConnections,
    required this.allowedOutputFormats,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      username: json['username'] ?? '',
      password: json['password'] ?? '',
      message: json['message'] ?? '',
      auth: json['auth']?.toString() ?? '',
      status: json['status'] ?? '',
      expDate: json['exp_date'] ?? '',
      isTrial: json['is_trial'] == '1',
      activeCons: int.tryParse(json['active_cons']?.toString() ?? '0') ?? 0,
      createdAt: json['created_at'] ?? '',
      maxConnections: int.tryParse(json['max_connections']?.toString() ?? '0') ?? 0,
      allowedOutputFormats: List<String>.from(json['allowed_output_formats'] ?? []),
    );
  }
}

class ServerInfo {
  final String url;
  final String port;
  final String httpsPort;
  final String serverProtocol;
  final String rtmpPort;
  final String timezone;
  final int timestampNow;
  final String timeNow;

  ServerInfo({
    required this.url,
    required this.port,
    required this.httpsPort,
    required this.serverProtocol,
    required this.rtmpPort,
    required this.timezone,
    required this.timestampNow,
    required this.timeNow,
  });

  factory ServerInfo.fromJson(Map<String, dynamic> json) {
    return ServerInfo(
      url: json['url'] ?? '',
      port: json['port'] ?? '',
      httpsPort: json['https_port'] ?? '',
      serverProtocol: json['server_protocol'] ?? '',
      rtmpPort: json['rtmp_port'] ?? '',
      timezone: json['timezone'] ?? '',
      timestampNow: int.tryParse(json['timestamp_now']?.toString() ?? '0') ?? 0,
      timeNow: json['time_now'] ?? '',
    );
  }
}

class LiveChannel {
  final int streamId;
  final int num;
  final String name;
  final String streamType;
  final String streamIcon;
  final int epgChannelId;
  final String added;
  final String categoryId;
  final String customSid;
  final int tvArchive;
  final String directSource;
  final int tvArchiveDuration;

  LiveChannel({
    required this.streamId,
    required this.num,
    required this.name,
    required this.streamType,
    required this.streamIcon,
    required this.epgChannelId,
    required this.added,
    required this.categoryId,
    required this.customSid,
    required this.tvArchive,
    required this.directSource,
    required this.tvArchiveDuration,
  });

  factory LiveChannel.fromJson(Map<String, dynamic> json) {
    return LiveChannel(
      streamId: _parseInt(json['stream_id']) ?? 0,
      num: _parseInt(json['num']) ?? 0,
      name: json['name'] ?? '',
      streamType: json['stream_type'] ?? '',
      streamIcon: json['stream_icon'] ?? '',
      epgChannelId: _parseInt(json['epg_channel_id']) ?? 0,
      added: json['added'] ?? '',
      categoryId: json['category_id'] ?? '',
      customSid: json['custom_sid'] ?? '',
      tvArchive: _parseInt(json['tv_archive']) ?? 0,
      directSource: json['direct_source'] ?? '',
      tvArchiveDuration: _parseInt(json['tv_archive_duration']) ?? 0,
    );
  }

  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }
}

class VodContent {
  final int streamId;
  final String name;
  final String streamIcon;
  final String rating;
  final int rating5based;
  final String added;
  final String categoryId;
  final String containerExtension;
  final String customSid;
  final String directSource;

  VodContent({
    required this.streamId,
    required this.name,
    required this.streamIcon,
    required this.rating,
    required this.rating5based,
    required this.added,
    required this.categoryId,
    required this.containerExtension,
    required this.customSid,
    required this.directSource,
  });

  factory VodContent.fromJson(Map<String, dynamic> json) {
    return VodContent(
      streamId: _parseInt(json['stream_id']) ?? 0,
      name: json['name'] ?? '',
      streamIcon: json['stream_icon'] ?? '',
      rating: json['rating'] ?? '',
      rating5based: _parseInt(json['rating_5based']) ?? 0,
      added: json['added'] ?? '',
      categoryId: json['category_id'] ?? '',
      containerExtension: json['container_extension'] ?? '',
      customSid: json['custom_sid'] ?? '',
      directSource: json['direct_source'] ?? '',
    );
  }

  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }
}

class IptvCategory {
  final String categoryId;
  final String categoryName;
  final String parentId;

  IptvCategory({
    required this.categoryId,
    required this.categoryName,
    required this.parentId,
  });

  factory IptvCategory.fromJson(Map<String, dynamic> json) {
    return IptvCategory(
      categoryId: json['category_id'] ?? '',
      categoryName: json['category_name'] ?? '',
      parentId: json['parent_id'] ?? '',
    );
  }
}

class EpgData {
  final String id;
  final String epgId;
  final String title;
  final String lang;
  final String start;
  final String end;
  final String description;
  final String channelId;
  final String startTimestamp;
  final String stopTimestamp;

  EpgData({
    required this.id,
    required this.epgId,
    required this.title,
    required this.lang,
    required this.start,
    required this.end,
    required this.description,
    required this.channelId,
    required this.startTimestamp,
    required this.stopTimestamp,
  });

  factory EpgData.fromJson(Map<String, dynamic> json) {
    return EpgData(
      id: json['id'] ?? '',
      epgId: json['epg_id'] ?? '',
      title: json['title'] ?? '',
      lang: json['lang'] ?? '',
      start: json['start'] ?? '',
      end: json['end'] ?? '',
      description: json['description'] ?? '',
      channelId: json['channel_id'] ?? '',
      startTimestamp: json['start_timestamp'] ?? '',
      stopTimestamp: json['stop_timestamp'] ?? '',
    );
  }
}
