#Fri Jul 18 21:20:01 EET 2025
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-anydpi-v21/ic_call_answer.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-anydpi-v21/ic_call_answer_low.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer_low.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-anydpi-v21/ic_call_answer_video.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer_video.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-anydpi-v21/ic_call_answer_video_low.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer_video_low.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-anydpi-v21/ic_call_decline.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_decline.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-anydpi-v21/ic_call_decline_low.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_decline_low.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer_video.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer_video_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_decline.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_decline_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/notification_bg_low_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_low_normal.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/notification_bg_low_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_low_pressed.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/notification_bg_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_normal.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_normal_pressed.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/notification_oversize_large_icon_bg.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_oversize_large_icon_bg.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-hdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notify_panel_notification_icon_bg.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-ldpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-ldpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-ldpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer_video.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-ldpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer_video_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-ldpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_decline.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-ldpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_decline_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer_video.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer_video_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_decline.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_decline_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/notification_bg_low_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_low_normal.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/notification_bg_low_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_low_pressed.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/notification_bg_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_normal.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_normal_pressed.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-mdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notify_panel_notification_icon_bg.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-v21/notification_action_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\notification_action_background.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer_video.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer_video_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_decline.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_decline_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/notification_bg_low_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_low_normal.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/notification_bg_low_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_low_pressed.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/notification_bg_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_normal.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_normal_pressed.9.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xhdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notify_panel_notification_icon_bg.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxhdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxhdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxhdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer_video.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxhdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer_video_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxhdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_decline.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxhdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_decline_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxxhdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxxhdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxxhdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer_video.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxxhdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer_video_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxxhdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_decline.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable-xxxhdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_decline_low.png
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable/notification_bg.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_bg.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable/notification_bg_low.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_bg_low.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable/notification_icon_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_icon_background.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/drawable/notification_tile_bg.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_tile_bg.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout-v21/notification_action.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_action.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout-v21/notification_action_tombstone.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_action_tombstone.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout-v21/notification_template_custom_big.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_template_custom_big.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout-v21/notification_template_icon_group.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_template_icon_group.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout/custom_dialog.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\custom_dialog.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout/ime_base_split_test_activity.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\ime_base_split_test_activity.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout/ime_secondary_split_test_activity.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\ime_secondary_split_test_activity.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout/notification_action.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_action.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout/notification_action_tombstone.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_action_tombstone.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout/notification_template_custom_big.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_custom_big.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout/notification_template_icon_group.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_icon_group.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout/notification_template_part_chronometer.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_part_chronometer.xml
com.tekartik.sqflite.sqflite_android-core-1.13.1-8\:/layout/notification_template_part_time.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_part_time.xml
com.tekartik.sqflite.sqflite_android-fragment-1.7.1-5\:/anim-v21/fragment_fast_out_extra_slow_in.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\fragment_fast_out_extra_slow_in.xml
com.tekartik.sqflite.sqflite_android-fragment-1.7.1-5\:/anim/fragment_fast_out_extra_slow_in.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\fragment_fast_out_extra_slow_in.xml
com.tekartik.sqflite.sqflite_android-fragment-1.7.1-5\:/animator/fragment_close_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_close_enter.xml
com.tekartik.sqflite.sqflite_android-fragment-1.7.1-5\:/animator/fragment_close_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_close_exit.xml
com.tekartik.sqflite.sqflite_android-fragment-1.7.1-5\:/animator/fragment_fade_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_fade_enter.xml
com.tekartik.sqflite.sqflite_android-fragment-1.7.1-5\:/animator/fragment_fade_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_fade_exit.xml
com.tekartik.sqflite.sqflite_android-fragment-1.7.1-5\:/animator/fragment_open_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_open_enter.xml
com.tekartik.sqflite.sqflite_android-fragment-1.7.1-5\:/animator/fragment_open_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\sqflite_android\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_open_exit.xml
