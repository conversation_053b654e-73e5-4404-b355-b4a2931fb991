androidx.media.AudioAttributesImplApi26
androidx.preference.PreferenceCategory
androidx.media3.exoplayer.dash.DashMediaSource$Factory
androidx.media3.ui.PlayerView
android.support.v4.media.AudioAttributesImplBaseParcelizer
android.support.v4.media.AudioAttributesImplApi21Parcelizer
androidx.core.graphics.drawable.IconCompat
androidx.appcompat.widget.SwitchCompat
androidx.appcompat.widget.ActionBarContextView
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
io.flutter.embedding.engine.FlutterOverlaySurface
androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory
android.support.v4.media.MediaMetadataCompat
androidx.media3.exoplayer.ExoPlayer
io.flutter.plugins.videoplayer.VideoPlayerPlugin
androidx.appcompat.widget.ActionBarOverlayLayout
androidx.core.app.CoreComponentFactory
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin
androidx.lifecycle.ProcessLifecycleOwner$attach$1
io.flutter.plugins.urllauncher.UrlLauncherPlugin
androidx.media.AudioAttributesImpl
androidx.lifecycle.ReportFragment
io.flutter.view.TextureRegistry$SurfaceTextureEntry
androidx.appcompat.widget.ActionBarContainer
androidx.preference.ListPreference
androidx.appcompat.widget.ActivityChooserView$InnerLayout
android.support.v4.media.AudioAttributesImplApi26Parcelizer
android.support.v4.media.session.MediaControllerCompat$MediaControllerImplApi21$ExtraBinderRequestResultReceiver
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
androidx.startup.InitializationProvider
androidx.core.widget.NestedScrollView
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
androidx.preference.Preference
androidx.appcompat.view.menu.ListMenuItemView
android.support.v4.graphics.drawable.IconCompatParcelizer
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
androidx.media3.exoplayer.image.ImageOutput
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
androidx.appcompat.widget.AlertDialogLayout
androidx.media.AudioAttributesImplBaseParcelizer
androidx.preference.internal.PreferenceImageView
androidx.appcompat.widget.Toolbar
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
androidx.annotation.Keep
androidx.preference.TwoStatePreference
io.flutter.plugins.GeneratedPluginRegistrant
androidx.appcompat.view.menu.ExpandedMenuView
androidx.recyclerview.widget.LinearLayoutManager
androidx.preference.PreferenceScreen
io.flutter.view.TextureRegistry$SurfaceProducer
androidx.media.AudioAttributesImplBase
androidx.media3.exoplayer.hls.HlsMediaSource$Factory
io.flutter.plugin.platform.SingleViewPresentation
android.support.v4.media.MediaBrowserCompat$ItemReceiver
io.flutter.view.TextureRegistry$ImageConsumer
kotlinx.coroutines.internal.StackTraceRecoveryKt
androidx.appcompat.widget.FitWindowsLinearLayout
androidx.preference.SwitchPreference
android.support.v4.media.MediaBrowserCompat$SearchResultReceiver
androidx.browser.browseractions.BrowserActionsFallbackMenuView
io.flutter.view.FlutterCallbackInformation
androidx.recyclerview.widget.GridLayoutManager
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
kotlinx.coroutines.android.AndroidDispatcherFactory
androidx.preference.CheckBoxPreference
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
androidx.lifecycle.ReportFragment$LifecycleCallbacks
io.flutter.view.TextureRegistry$ImageTextureEntry
androidx.appcompat.widget.ContentFrameLayout
android.support.v4.media.session.MediaSessionCompat$ResultReceiverWrapper
androidx.core.app.RemoteActionCompat
androidx.appcompat.widget.SearchView$SearchAutoComplete
androidx.media3.ui.SubtitleView
androidx.versionedparcelable.ParcelImpl
androidx.core.app.RemoteActionCompatParcelizer
androidx.window.extensions.core.util.function.Consumer
androidx.media3.ui.DefaultTimeBar
android.support.v4.media.MediaDescriptionCompat
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
androidx.core.graphics.drawable.IconCompatParcelizer
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
android.support.v4.media.session.PlaybackStateCompat$CustomAction
androidx.preference.EditTextPreference
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
androidx.lifecycle.ProcessLifecycleInitializer
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
io.flutter.view.AccessibilityViewEmbedder
androidx.preference.SeekBarPreference
android.support.v4.media.AudioAttributesCompatParcelizer
androidx.appcompat.widget.ViewStubCompat
android.support.v4.media.session.ParcelableVolumeInfo
androidx.media.AudioAttributesImplApi26Parcelizer
android.support.v4.media.MediaBrowserCompat$MediaItem
androidx.profileinstaller.ProfileInstallReceiver
androidx.preference.SwitchPreferenceCompat
androidx.appcompat.app.AlertController$RecycleListView
androidx.media.AudioAttributesCompat
com.example.iptv_player.ExoPlayerActivity
io.flutter.view.TextureRegistry$GLTextureConsumer
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
io.flutter.plugin.text.ProcessTextPlugin
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
androidx.appcompat.view.menu.ActionMenuItemView
android.support.v4.media.RatingCompat
androidx.appcompat.widget.ActionMenuView
androidx.preference.DialogPreference
android.support.v4.media.session.PlaybackStateCompat
androidx.window.extensions.core.util.function.Predicate
android.support.v4.media.session.MediaSessionCompat$QueueItem
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView
androidx.appcompat.widget.SearchView
android.support.v4.media.session.MediaSessionCompat$Token
androidx.versionedparcelable.CustomVersionedParcelable
android.support.v4.media.MediaBrowserCompat$CustomActionResultReceiver
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin
androidx.media3.ui.TrackSelectionView
androidx.media.AudioAttributesImplApi21
com.tekartik.sqflite.SqflitePlugin
androidx.preference.UnPressableLinearLayout
com.example.iptv_player.MainActivity
androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView
io.flutter.plugins.pathprovider.PathProviderPlugin
androidx.profileinstaller.ProfileInstallerInitializer
androidx.appcompat.widget.FitWindowsFrameLayout
androidx.preference.MultiSelectListPreference
kotlin.coroutines.jvm.internal.BaseContinuationImpl
androidx.window.extensions.core.util.function.Function
android.support.v4.app.RemoteActionCompatParcelizer
androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory
androidx.appcompat.widget.ButtonBarLayout
androidx.recyclerview.widget.RecyclerView
io.flutter.embedding.engine.FlutterJNI
androidx.preference.PreferenceGroup
androidx.media.AudioAttributesImplApi21Parcelizer
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
androidx.appcompat.widget.DialogTitle
io.flutter.plugins.urllauncher.WebViewActivity
androidx.recyclerview.widget.StaggeredGridLayoutManager
androidx.media.AudioAttributesCompatParcelizer
androidx.media3.ui.AspectRatioFrameLayout
androidx.preference.DropDownPreference
android.support.v4.media.MediaBrowserCompat$MediaItem: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.vorbis.VorbisComment: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.InvokeOnCancelling: int _invoked
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
kotlinx.coroutines.channels.BufferedChannel: long receivers
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
androidx.media3.extractor.metadata.scte35.PrivateCommand: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
androidx.media3.extractor.metadata.mp4.MotionPhotoMetadata: android.os.Parcelable$Creator CREATOR
android.support.v4.media.session.MediaSessionCompat$ResultReceiverWrapper: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
androidx.media3.extractor.metadata.scte35.TimeSignalCommand: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
io.flutter.embedding.engine.FlutterJNI: float displayDensity
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
androidx.media3.extractor.metadata.emsg.EventMessage: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
androidx.media3.extractor.metadata.dvbsi.AppInfoTable: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.id3.ApicFrame: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
androidx.media3.exoplayer.hls.HlsTrackMetadataEntry: android.os.Parcelable$Creator CREATOR
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
android.support.v4.media.session.PlaybackStateCompat: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
android.support.v4.media.MediaMetadataCompat: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
androidx.media3.extractor.metadata.mp4.SlowMotionData: android.os.Parcelable$Creator CREATOR
androidx.media3.common.DrmInitData: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
androidx.media3.extractor.metadata.scte35.SpliceNullCommand: android.os.Parcelable$Creator CREATOR
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
kotlinx.coroutines.CompletedExceptionally: int _handled
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
kotlinx.coroutines.DefaultExecutor: int debugStatus
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
androidx.media3.extractor.metadata.id3.InternalFrame: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
androidx.media3.extractor.metadata.id3.TextInformationFrame: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
io.flutter.embedding.engine.FlutterJNI: float displayHeight
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
android.support.v4.media.session.PlaybackStateCompat$CustomAction: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
androidx.datastore.preferences.PreferencesProto$Value: int BYTES_FIELD_NUMBER
androidx.media3.extractor.metadata.scte35.SpliceScheduleCommand: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.id3.ChapterTocFrame: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.JobSupport: java.lang.Object _state
androidx.media3.common.DrmInitData$SchemeData: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage lastDequeuedImage
androidx.media3.extractor.metadata.id3.CommentFrame: android.os.Parcelable$Creator CREATOR
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
androidx.media3.extractor.metadata.id3.BinaryFrame: android.os.Parcelable$Creator CREATOR
androidx.media3.common.StreamKey: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
android.support.v4.os.ResultReceiver: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.id3.PrivFrame: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
android.support.v4.media.MediaDescriptionCompat: android.os.Parcelable$Creator CREATOR
android.support.v4.media.session.MediaSessionCompat$QueueItem: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
androidx.media3.extractor.metadata.mp4.SlowMotionData$Segment: android.os.Parcelable$Creator CREATOR
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
kotlinx.coroutines.CancelledContinuation: int _resumed
androidx.media3.extractor.metadata.id3.GeobFrame: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
android.support.v4.media.RatingCompat: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.media3.extractor.metadata.icy.IcyInfo: android.os.Parcelable$Creator CREATOR
android.support.v4.media.session.ParcelableVolumeInfo: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
io.flutter.embedding.engine.FlutterOverlaySurface: int id
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
androidx.media3.container.Mp4LocationData: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
androidx.media3.extractor.metadata.flac.PictureFrame: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
io.flutter.embedding.engine.FlutterJNI: float displayWidth
androidx.media3.container.Mp4TimestampData: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
kotlinx.coroutines.DispatchedCoroutine: int _decision
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
androidx.media3.container.MdtaMetadataEntry: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
androidx.media3.extractor.metadata.id3.ChapterFrame: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
androidx.media3.extractor.metadata.icy.IcyHeaders: android.os.Parcelable$Creator CREATOR
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
androidx.media3.extractor.metadata.mp4.SmtaMetadataEntry: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
androidx.media3.extractor.metadata.id3.UrlLinkFrame: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
io.flutter.plugin.platform.SingleViewPresentation: int viewId
androidx.media3.exoplayer.hls.HlsTrackMetadataEntry$VariantInfo: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
androidx.media3.extractor.metadata.scte35.SpliceInsertCommand: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.id3.MlltFrame: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
androidx.media3.common.Metadata: android.os.Parcelable$Creator CREATOR
android.support.v4.media.session.MediaSessionCompat$Token: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
androidx.media3.extractor.metadata.flac.VorbisComment: android.os.Parcelable$Creator CREATOR
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.String getMediaId(android.media.MediaDescription)
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
androidx.media3.ui.SubtitleView: void setViewType(int)
androidx.media3.ui.PlayerControlView: void setAnimationEnabled(boolean)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
androidx.media.AudioAttributesCompatParcelizer: androidx.media.AudioAttributesCompat read(androidx.versionedparcelable.VersionedParcel)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
androidx.media3.ui.PlayerView: void setAspectRatioListener(androidx.media3.ui.AspectRatioFrameLayout$AspectRatioListener)
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
androidx.media3.exoplayer.audio.DefaultAudioOffloadSupportProvider$Api31: androidx.media3.exoplayer.audio.AudioOffloadSupport getOffloadedPlaybackSupport(android.media.AudioFormat,android.media.AudioAttributes,boolean)
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
androidx.media3.ui.PlayerView: boolean getControllerAutoShow()
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.String permissionToOp(java.lang.String)
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
androidx.media3.exoplayer.ExoPlayerImpl$Api23: void registerAudioDeviceCallback(android.media.AudioManager,android.media.AudioDeviceCallback,android.os.Handler)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.media3.ui.TrackSelectionView: void setShowDisableOption(boolean)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
androidx.media3.ui.TrackSelectionView: void setAllowAdaptiveSelections(boolean)
androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView: VideoDecoderGLSurfaceView(android.content.Context)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.CharSequence getDescription(android.media.MediaDescription)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
androidx.media3.ui.PlayerView: void setImage(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
androidx.media3.ui.PlayerView: void setControllerVisibilityListener(androidx.media3.ui.PlayerView$ControllerVisibilityListener)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.graphics.Bitmap getIconBitmap(android.media.MediaDescription)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.media3.exoplayer.audio.DefaultAudioSink$OnRoutingChangedListenerApi24: void onRoutingChanged(android.media.AudioRouting)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.core.widget.NestedScrollView: int getScrollRange()
com.google.common.collect.AbstractMapEntry: AbstractMapEntry()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
androidx.media3.exoplayer.audio.AudioCapabilitiesReceiver$Api23: void registerAudioDeviceCallback(android.content.Context,android.media.AudioDeviceCallback,android.os.Handler)
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
android.support.v4.media.AudioAttributesImplBaseParcelizer: androidx.media.AudioAttributesImplBase read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView: androidx.media3.exoplayer.video.VideoDecoderOutputBufferRenderer getVideoDecoderOutputBufferRenderer()
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
androidx.media3.exoplayer.hls.HlsMediaSource$Factory: HlsMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
androidx.media3.ui.PlayerView: void setRepeatToggleModes(int)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
androidx.media3.ui.PlayerControlView: androidx.media3.common.Player getPlayer()
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
androidx.media3.exoplayer.audio.DefaultAudioSink$Api23: void setPreferredDeviceOnAudioTrack(android.media.AudioTrack,androidx.media3.exoplayer.audio.AudioDeviceInfoApi23)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
androidx.media3.ui.PlayerView: void setErrorMessageProvider(androidx.media3.common.ErrorMessageProvider)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.core.app.AppOpsManagerCompat$Api29Impl: android.app.AppOpsManager getSystemService(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
io.flutter.embedding.android.FlutterView: void setVisibility(int)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
androidx.media.AudioAttributesImplApi21: AudioAttributesImplApi21()
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
androidx.media3.ui.PlayerView: java.util.List getAdOverlayInfos()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
androidx.media3.ui.TrackSelectionView: void setTrackNameProvider(androidx.media3.ui.TrackNameProvider)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
androidx.media3.exoplayer.ExoPlayerImpl$Api23: boolean isSuitableAudioOutputPresentInAudioDeviceInfoList(android.content.Context,android.media.AudioDeviceInfo[])
androidx.media3.ui.PlayerView$SurfaceSyncGroupCompatV34: void postRegister(android.os.Handler,android.view.SurfaceView,java.lang.Runnable)
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
androidx.media3.exoplayer.audio.DefaultAudioSink$OnRoutingChangedListenerApi24: void release()
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
androidx.media3.ui.PlayerView: void setShowRewindButton(boolean)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void lambda$dequeueImage$0()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
androidx.media3.ui.DefaultTimeBar: long getScrubberPosition()
androidx.media3.ui.SubtitleView: void setView(android.view.View)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
androidx.appcompat.widget.Toolbar: void setTitle(int)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.media3.ui.PlayerView: void setControllerOnFullScreenModeChangedListener(androidx.media3.ui.PlayerControlView$OnFullScreenModeChangedListener)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
androidx.media3.ui.PlayerControlView: void setShowVrButton(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: androidx.media.AudioAttributesImplApi26 read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
androidx.media3.ui.DefaultTimeBar: void setBufferedColor(int)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
com.google.common.collect.Iterators$EmptyModifiableIterator: com.google.common.collect.Iterators$EmptyModifiableIterator valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
android.support.v4.media.AudioAttributesCompatParcelizer: androidx.media.AudioAttributesCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
androidx.media.AudioAttributesCompat: AudioAttributesCompat()
androidx.core.content.ContextCompat$Api33Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: android.view.Surface getVideoSurface()
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
androidx.media3.ui.PlayerControlView: void setVrButtonListener(android.view.View$OnClickListener)
androidx.browser.customtabs.CustomTabsIntent$Api24Impl: java.lang.String getDefaultLocale()
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
androidx.exifinterface.media.ExifInterfaceUtils$Api23Impl: void setDataSource(android.media.MediaMetadataRetriever,android.media.MediaDataSource)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.media3.exoplayer.image.ImageOutput: void onDisabled()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.CharSequence getSubtitle(android.media.MediaDescription)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setSubtitle(android.media.MediaDescription$Builder,java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
androidx.media3.ui.SubtitleView: void setCues(java.util.List)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
androidx.media3.common.AudioAttributes$Api29: void setAllowedCapturePolicy(android.media.AudioAttributes$Builder,int)
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
androidx.media3.ui.PlayerControlView: void setShowMultiWindowTimeBar(boolean)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
android.support.v4.media.AudioAttributesImplBaseParcelizer: void write(androidx.media.AudioAttributesImplBase,androidx.versionedparcelable.VersionedParcel)
androidx.media3.ui.DefaultTimeBar: void setScrubberColor(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
androidx.media3.ui.PlayerView: boolean getControllerHideOnTouch()
android.support.v4.media.MediaDescriptionCompat$Api23Impl: android.net.Uri getMediaUri(android.media.MediaDescription)
io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat: io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat valueOf(java.lang.String)
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView: void setOutputBuffer(androidx.media3.decoder.VideoDecoderOutputBuffer)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
androidx.media3.exoplayer.audio.DefaultAudioSink$StreamEventCallbackV29: void register(android.media.AudioTrack)
androidx.media3.ui.PlayerView: android.view.View getVideoSurfaceView()
com.google.common.util.concurrent.DirectExecutor: com.google.common.util.concurrent.DirectExecutor[] values()
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setIconBitmap(android.media.MediaDescription$Builder,android.graphics.Bitmap)
com.google.common.collect.Ordering: Ordering()
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
androidx.recyclerview.widget.RecyclerView: void setLayoutTransition(android.animation.LayoutTransition)
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
androidx.core.content.ContextCompat$Api26Impl: android.content.ComponentName startForegroundService(android.content.Context,android.content.Intent)
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.media3.ui.SubtitleView: void setApplyEmbeddedFontSizes(boolean)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
androidx.media.AudioAttributesImplApi26: AudioAttributesImplApi26()
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.media3.ui.DefaultTimeBar: void setPosition(long)
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
androidx.media3.ui.PlayerView: androidx.media3.ui.SubtitleView getSubtitleView()
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
androidx.media3.ui.PlayerView: void setUseArtwork(boolean)
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
androidx.media3.ui.DefaultTimeBar: java.lang.String getProgressText()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.media3.ui.DefaultTimeBar: void setDuration(long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
androidx.media.AudioAttributesCompatParcelizer: void write(androidx.media.AudioAttributesCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
androidx.media3.ui.SubtitleView: java.util.List getCuesWithStylingPreferencesApplied()
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
androidx.media3.ui.PlayerView$Api34: void setSurfaceLifecycleToFollowsAttachment(android.view.SurfaceView)
com.google.common.collect.Iterators$EmptyModifiableIterator: com.google.common.collect.Iterators$EmptyModifiableIterator[] values()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.media3.ui.PlayerView: void setVisibility(int)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.media3.ui.DefaultTimeBar: void setPlayedColor(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.media3.ui.DefaultTimeBar: DefaultTimeBar(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: java.io.FileDescriptor dup(java.io.FileDescriptor)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List)
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
androidx.media3.ui.PlayerControlView: void setShowNextButton(boolean)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
androidx.media3.ui.PlayerView: PlayerView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$Api21: boolean registerOnBufferAvailableListener(androidx.media3.exoplayer.mediacodec.MediaCodecAdapter,androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$MediaCodecRendererCodecAdapterListener)
com.tekartik.sqflite.SqflitePlugin: SqflitePlugin()
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: void setUseSensorRotation(boolean)
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference)
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
androidx.media3.common.util.Util$Api21: android.graphics.drawable.Drawable getDrawable(android.content.Context,android.content.res.Resources,int)
android.support.v4.media.AudioAttributesImplBaseParcelizer: AudioAttributesImplBaseParcelizer()
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
com.google.common.collect.AbstractMultimap: AbstractMultimap()
io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees: io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees valueOf(java.lang.String)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
androidx.media3.exoplayer.audio.DefaultAudioOffloadSupportProvider$Api29: androidx.media3.exoplayer.audio.AudioOffloadSupport getOffloadedPlaybackSupport(android.media.AudioFormat,android.media.AudioAttributes,boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
android.support.v4.media.AudioAttributesCompatParcelizer: void write(androidx.media.AudioAttributesCompat,androidx.versionedparcelable.VersionedParcel)
androidx.media3.ui.PlayerView: void setShowShuffleButton(boolean)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.media3.ui.AspectRatioFrameLayout: void setAspectRatio(float)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
android.support.v4.media.AudioAttributesImplApi26Parcelizer: AudioAttributesImplApi26Parcelizer()
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
io.flutter.plugins.videoplayer.VideoPlayerPlugin: VideoPlayerPlugin()
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType: io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType[] values()
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
androidx.media3.exoplayer.ExoPlayer: void setImageOutput(androidx.media3.exoplayer.image.ImageOutput)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
androidx.media3.ui.PlayerView: void setShowPreviousButton(boolean)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView$Adapter$StateRestorationPolicy: androidx.recyclerview.widget.RecyclerView$Adapter$StateRestorationPolicy valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
androidx.media.AudioAttributesImplBaseParcelizer: void write(androidx.media.AudioAttributesImplBase,androidx.versionedparcelable.VersionedParcel)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
androidx.media3.ui.PlayerView: void setCustomErrorMessage(java.lang.CharSequence)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setExtras(android.media.MediaDescription$Builder,android.os.Bundle)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
androidx.media3.ui.DefaultTimeBar: long getPositionIncrement()
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory: RtspMediaSource$Factory()
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
androidx.browser.browseractions.BrowserActionsFallbackMenuView: BrowserActionsFallbackMenuView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory: SsMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.media3.ui.PlayerView: int getResizeMode()
androidx.media3.ui.PlayerView: void setKeepContentOnPlayerReset(boolean)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
androidx.core.app.AppOpsManagerCompat$Api29Impl: java.lang.String getOpPackageName(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
androidx.media3.ui.PlayerView: boolean getUseController()
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.browser.customtabs.CustomTabsIntent$Api23Impl: android.app.ActivityOptions makeBasicActivityOptions()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: androidx.media.AudioAttributesImplApi21 read(androidx.versionedparcelable.VersionedParcel)
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOpNoThrow(android.app.AppOpsManager,java.lang.String,java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: androidx.media3.exoplayer.video.VideoFrameMetadataListener getVideoFrameMetadataListener()
androidx.core.app.AppOpsManagerCompat$Api29Impl: int checkOpNoThrow(android.app.AppOpsManager,java.lang.String,int,java.lang.String)
androidx.media.AudioAttributesImplApi26Parcelizer: androidx.media.AudioAttributesImplApi26 read(androidx.versionedparcelable.VersionedParcel)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: void write(androidx.media.AudioAttributesImplApi26,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.media3.ui.PlayerView: void setShowNextButton(boolean)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
androidx.media3.exoplayer.audio.DefaultAudioSink$Api31: void setLogSessionIdOnAudioTrack(android.media.AudioTrack,androidx.media3.exoplayer.analytics.PlayerId)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
androidx.media3.ui.PlayerControlView: void setShowPlayButtonIfPlaybackIsSuppressed(boolean)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
androidx.media3.exoplayer.video.MediaCodecVideoRenderer$Api26: boolean doesDisplaySupportDolbyVision(android.content.Context)
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
androidx.appcompat.widget.SearchView: int getInputType()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
androidx.media3.ui.PlayerView: void setDefaultArtwork(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
androidx.media3.ui.SubtitleView: void setBottomPaddingFraction(float)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
androidx.appcompat.widget.SearchView: int getPreferredHeight()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
com.example.iptv_player.MainActivity: MainActivity()
androidx.media3.ui.PlayerView: android.view.ViewGroup getAdViewGroup()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
androidx.media3.ui.PlayerControlView: void setTimeBarMinUpdateInterval(int)
androidx.appcompat.widget.SearchView: int getMaxWidth()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
androidx.media3.ui.PlayerView: void setControllerAutoShow(boolean)
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
androidx.media.AudioAttributesImplApi21Parcelizer: AudioAttributesImplApi21Parcelizer()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
androidx.media3.ui.TrackSelectionView: java.util.Map getOverrides()
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
androidx.media3.ui.TrackSelectionView: void setAllowMultipleOverrides(boolean)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.media3.exoplayer.ExoPlayerImpl$Api31: androidx.media3.exoplayer.analytics.PlayerId registerMediaMetricsListener(android.content.Context,androidx.media3.exoplayer.ExoPlayerImpl,boolean,java.lang.String)
com.google.common.collect.AbstractIterator$State: com.google.common.collect.AbstractIterator$State[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
androidx.appcompat.widget.SearchView: void setImeOptions(int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
com.google.common.collect.AbstractIterator$State: com.google.common.collect.AbstractIterator$State valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
androidx.appcompat.widget.SearchView: int getPreferredWidth()
androidx.media3.ui.SubtitleView: void setFractionalTextSize(float)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
androidx.media3.ui.DefaultTimeBar: long getPreferredUpdateDelay()
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setIconUri(android.media.MediaDescription$Builder,android.net.Uri)
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
androidx.media.AudioAttributesImplBase: AudioAttributesImplBase()
androidx.media3.ui.SubtitleView: void setStyle(androidx.media3.ui.CaptionStyleCompat)
androidx.media.AudioAttributesCompatParcelizer: AudioAttributesCompatParcelizer()
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
androidx.media3.ui.PlayerControlView: void setPlaybackSpeed(float)
com.google.common.base.Function: boolean equals(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
androidx.media3.ui.PlayerView: void setShowVrButton(boolean)
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
androidx.media3.ui.PlayerView: void setShowMultiWindowTimeBar(boolean)
androidx.media3.ui.PlayerView: android.widget.FrameLayout getOverlayFrameLayout()
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
com.google.common.base.AbstractIterator$State: com.google.common.base.AbstractIterator$State[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
androidx.media3.ui.PlayerControlView: boolean getShowSubtitleButton()
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.media3.ui.PlayerView: void setShowPlayButtonIfPlaybackIsSuppressed(boolean)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.media3.ui.AspectRatioFrameLayout: void setAspectRatioListener(androidx.media3.ui.AspectRatioFrameLayout$AspectRatioListener)
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.SearchView: void setIconified(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.media3.ui.PlayerControlView: int getShowTimeoutMs()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
androidx.media3.common.AudioAttributes$Api32: void setSpatializationBehavior(android.media.AudioAttributes$Builder,int)
androidx.media3.ui.DefaultTimeBar: void setPlayedAdMarkerColor(int)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
io.flutter.plugins.urllauncher.WebViewActivity: WebViewActivity()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setDescription(android.media.MediaDescription$Builder,java.lang.CharSequence)
androidx.media3.ui.PlayerView: void setControllerHideDuringAds(boolean)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.media3.ui.PlayerControlView: void setShowShuffleButton(boolean)
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
androidx.media3.exoplayer.drm.DrmUtil$Api23: boolean isMediaDrmResetException(java.lang.Throwable)
androidx.media3.exoplayer.audio.AudioCapabilities$Api23: boolean isBluetoothConnected(android.media.AudioManager,androidx.media3.exoplayer.audio.AudioDeviceInfoApi23)
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.media3.ui.SubtitleView: SubtitleView(android.content.Context,android.util.AttributeSet)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.media.MediaDescription build(android.media.MediaDescription$Builder)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
androidx.media3.datasource.RawResourceDataSource: android.net.Uri buildRawResourceUri(int)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: SphericalGLSurfaceView(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
androidx.media3.ui.DefaultTimeBar: void setEnabled(boolean)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin: PackageInfoPlugin()
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
androidx.media3.ui.PlayerView: void setArtworkDisplayMode(int)
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
com.google.common.collect.BaseImmutableMultimap: BaseImmutableMultimap()
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: void setDefaultStereoMode(int)
androidx.media3.ui.PlayerView: androidx.media3.common.Player getPlayer()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
androidx.media3.ui.PlayerView: void setImageDisplayMode(int)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
androidx.media3.ui.PlayerControlView: void setPlayer(androidx.media3.common.Player)
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.media.MediaDescription$Builder createBuilder()
io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType: io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.media3.exoplayer.audio.AudioCapabilities$Api23: com.google.common.collect.ImmutableSet getAllBluetoothDeviceTypes()
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.media3.ui.SubtitleView: androidx.media3.ui.CaptionStyleCompat getUserCaptionStyle()
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOp(android.app.AppOpsManager,java.lang.String,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.media3.ui.PlayerView: void setImageOutput(androidx.media3.common.Player)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
androidx.media3.ui.PlayerControlView: void setShowSubtitleButton(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
androidx.media3.ui.DefaultTimeBar: void setKeyTimeIncrement(long)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
com.google.common.base.AbstractIterator$State: com.google.common.base.AbstractIterator$State valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$Api31: void setLogSessionIdToMediaCodecFormat(androidx.media3.exoplayer.mediacodec.MediaCodecAdapter$Configuration,androidx.media3.exoplayer.analytics.PlayerId)
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
androidx.media3.ui.PlayerView: void setShowBuffering(int)
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.appcompat.widget.SearchView: void setInputType(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
androidx.media3.exoplayer.audio.AudioCapabilitiesReceiver$Api23: void unregisterAudioDeviceCallback(android.content.Context,android.media.AudioDeviceCallback)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setMediaId(android.media.MediaDescription$Builder,java.lang.String)
androidx.media3.ui.AspectRatioFrameLayout: void setResizeMode(int)
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
androidx.core.content.ContextCompat$Api26Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
android.support.v4.media.AudioAttributesCompatParcelizer: AudioAttributesCompatParcelizer()
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.media3.ui.TrackSelectionView: TrackSelectionView(android.content.Context,android.util.AttributeSet)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.net.Uri getIconUri(android.media.MediaDescription)
androidx.media3.exoplayer.audio.AudioCapabilities$Api33: androidx.media3.exoplayer.audio.AudioDeviceInfoApi23 getDefaultRoutedDeviceForAttributes(android.media.AudioManager,androidx.media3.common.AudioAttributes)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.media3.ui.PlayerControlView: void setShowRewindButton(boolean)
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.os.Bundle getExtras(android.media.MediaDescription)
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.media3.ui.PlayerView$SurfaceSyncGroupCompatV34: void maybeMarkSyncReadyAndClear()
androidx.media3.ui.PlayerControlView: void setShowFastForwardButton(boolean)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
androidx.media3.ui.PlayerView: void setShowSubtitleButton(boolean)
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
io.flutter.plugins.urllauncher.UrlLauncherPlugin: UrlLauncherPlugin()
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.media3.ui.PlayerControlView: void setProgressUpdateListener(androidx.media3.ui.PlayerControlView$ProgressUpdateListener)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.media.AudioAttributesImplApi21Parcelizer: void write(androidx.media.AudioAttributesImplApi21,androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView$Adapter$StateRestorationPolicy: androidx.recyclerview.widget.RecyclerView$Adapter$StateRestorationPolicy[] values()
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
androidx.media3.ui.PlayerView: void setControllerVisibilityListener(androidx.media3.ui.PlayerControlView$VisibilityListener)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.media.AudioAttributesImplBaseParcelizer: AudioAttributesImplBaseParcelizer()
androidx.media.AudioAttributesImplApi26Parcelizer: void write(androidx.media.AudioAttributesImplApi26,androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat: io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat[] values()
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
androidx.media3.ui.PlayerControlView: void setShowTimeoutMs(int)
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.media3.exoplayer.dash.DashMediaSource$Factory: DashMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
androidx.media3.exoplayer.audio.AudioCapabilities$Api33: androidx.media3.exoplayer.audio.AudioCapabilities getCapabilitiesInternalForDirectPlayback(android.media.AudioManager,androidx.media3.common.AudioAttributes)
androidx.recyclerview.widget.RecyclerView: java.lang.CharSequence getAccessibilityClassName()
kotlin.collections.AbstractList: AbstractList()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.media3.exoplayer.drm.FrameworkMediaDrm$Api31: void setLogSessionIdOnMediaDrmSession(android.media.MediaDrm,byte[],androidx.media3.exoplayer.analytics.PlayerId)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: long lseek(java.io.FileDescriptor,long,int)
androidx.media3.ui.PlayerView: void setFullscreenButtonClickListener(androidx.media3.ui.PlayerView$FullscreenButtonClickListener)
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
androidx.recyclerview.widget.RecyclerView: int getBaseline()
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
androidx.media3.ui.DefaultTimeBar: void setBufferedPosition(long)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.appcompat.widget.SearchView: int getImeOptions()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
androidx.media3.ui.PlayerView: void setShutterBackgroundColor(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.media3.ui.PlayerView: void setPlayer(androidx.media3.common.Player)
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.startup.InitializationProvider: InitializationProvider()
androidx.media3.ui.AspectRatioFrameLayout: AspectRatioFrameLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: void close(java.io.FileDescriptor)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
androidx.media3.ui.PlayerView: void setControllerShowTimeoutMs(int)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
androidx.media3.ui.SubtitleView: float getUserCaptionFontScale()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
androidx.media3.ui.DefaultTimeBar: void setUnplayedColor(int)
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
androidx.media3.ui.TrackSelectionView: boolean getIsDisabled()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
androidx.media3.ui.PlayerControlView: void setShowPreviousButton(boolean)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.media.AudioAttributesImplApi26Parcelizer: AudioAttributesImplApi26Parcelizer()
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
androidx.appcompat.widget.Toolbar: void setLogo(int)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
androidx.media3.ui.PlayerControlView: boolean getShowShuffleButton()
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: androidx.media3.exoplayer.video.spherical.CameraMotionListener getCameraMotionListener()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
androidx.media3.ui.DefaultTimeBar: void setKeyCountIncrement(int)
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
androidx.media3.exoplayer.drm.DrmUtil$Api21: boolean isMediaDrmStateException(java.lang.Throwable)
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
androidx.media3.ui.PlayerControlView: int getRepeatToggleModes()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$700(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
androidx.media3.ui.PlayerView: android.graphics.drawable.Drawable getDefaultArtwork()
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setTitle(android.media.MediaDescription$Builder,java.lang.CharSequence)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
androidx.media3.ui.PlayerView: boolean getUseArtwork()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction[] values()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.media3.ui.PlayerView: int getImageDisplayMode()
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
androidx.recyclerview.widget.RecyclerView: boolean isLayoutSuppressed()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType[] values()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType valueOf(java.lang.String)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.browser.customtabs.CustomTabsIntent$Api34Impl: void setShareIdentityEnabled(android.app.ActivityOptions,boolean)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: void write(androidx.media.AudioAttributesImplApi21,androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
androidx.media3.datasource.FileDataSource$Api21: boolean isPermissionError(java.lang.Throwable)
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.media3.exoplayer.image.ImageOutput: void onImageAvailable(long,android.graphics.Bitmap)
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.media.AudioAttributesImplApi21Parcelizer: androidx.media.AudioAttributesImplApi21 read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
androidx.media3.ui.PlayerControlView: void setRepeatToggleModes(int)
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
androidx.media3.ui.PlayerControlView: boolean getShowVrButton()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.media3.exoplayer.video.VideoFrameReleaseHelper$Api30: void setSurfaceFrameRate(android.view.Surface,float)
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.media3.ui.PlayerView: void setControllerHideOnTouch(boolean)
androidx.media3.exoplayer.audio.MediaCodecAudioRenderer$Api23: void setAudioSinkPreferredDevice(androidx.media3.exoplayer.audio.AudioSink,java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.CharSequence getTitle(android.media.MediaDescription)
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
androidx.media3.exoplayer.audio.DefaultAudioSink$StreamEventCallbackV29: void unregister(android.media.AudioTrack)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
androidx.media3.exoplayer.audio.AudioCapabilities$Api29: com.google.common.collect.ImmutableList getDirectPlaybackSupportedEncodings(androidx.media3.common.AudioAttributes)
com.example.iptv_player.ExoPlayerActivity: ExoPlayerActivity()
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
com.google.common.base.Function: java.lang.Object apply(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
android.support.v4.media.MediaDescriptionCompat$Api23Impl: void setMediaUri(android.media.MediaDescription$Builder,android.net.Uri)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
androidx.media3.ui.AspectRatioFrameLayout: int getResizeMode()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
androidx.media3.ui.PlayerView: void setResizeMode(int)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
android.support.v4.media.AudioAttributesImplApi21Parcelizer: AudioAttributesImplApi21Parcelizer()
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
androidx.media3.ui.PlayerView: void setShowFastForwardButton(boolean)
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
androidx.lifecycle.ReportFragment: ReportFragment()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
androidx.media3.ui.PlayerControlView: void setOnFullScreenModeChangedListener(androidx.media3.ui.PlayerControlView$OnFullScreenModeChangedListener)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
androidx.media3.ui.PlayerView: void setControllerAnimationEnabled(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
com.google.common.util.concurrent.DirectExecutor: com.google.common.util.concurrent.DirectExecutor valueOf(java.lang.String)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
androidx.media3.ui.PlayerView: int getArtworkDisplayMode()
androidx.media3.exoplayer.drm.DrmUtil$Api21: int mediaDrmStateExceptionToErrorCode(java.lang.Throwable)
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
androidx.media3.ui.DefaultTimeBar: void setAdMarkerColor(int)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.media3.ui.PlayerView: void setUseController(boolean)
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.recyclerview.widget.RecyclerView: void suppressLayout(boolean)
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
androidx.media3.ui.PlayerView: int getControllerShowTimeoutMs()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
androidx.media3.exoplayer.audio.AudioCapabilities$Api29: int getMaxSupportedChannelCountForPassthrough(int,int,androidx.media3.common.AudioAttributes)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.media3.ui.SubtitleView: void setApplyEmbeddedStyles(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
androidx.media3.exoplayer.mediacodec.MediaCodecPerformancePointCoverageProvider$Api29: int areResolutionAndFrameRateCovered(android.media.MediaCodecInfo$VideoCapabilities,int,int,double)
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
kotlin.random.Random: Random()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
androidx.media.AudioAttributesImplBaseParcelizer: androidx.media.AudioAttributesImplBase read(androidx.versionedparcelable.VersionedParcel)
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin: WakelockPlusPlugin()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
androidx.media3.exoplayer.drm.FrameworkMediaDrm$Api31: boolean requiresSecureDecoder(android.media.MediaDrm,java.lang.String)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees: io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
