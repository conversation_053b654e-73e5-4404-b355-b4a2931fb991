androidx.core.graphics.drawable.IconCompatParcelizer
android.support.v4.media.session.PlaybackStateCompat$CustomAction
androidx.appcompat.widget.FitWindowsFrameLayout
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin
androidx.media3.ui.AspectRatioFrameLayout
androidx.appcompat.widget.SwitchCompat
io.flutter.plugins.pathprovider.PathProviderPlugin
androidx.media3.ui.TrackSelectionView
androidx.appcompat.widget.SearchView
androidx.core.app.CoreComponentFactory
androidx.appcompat.widget.ViewStubCompat
androidx.appcompat.widget.ButtonBarLayout
androidx.core.app.RemoteActionCompat
androidx.appcompat.widget.ActivityChooserView$InnerLayout
android.support.v4.media.session.MediaSessionCompat$ResultReceiverWrapper
androidx.preference.DialogPreference
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
io.flutter.view.TextureRegistry$ImageConsumer
io.flutter.embedding.engine.FlutterOverlaySurface
androidx.preference.SeekBarPreference
androidx.media3.ui.DefaultTimeBar
androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory
androidx.lifecycle.ReportFragment$LifecycleCallbacks
androidx.appcompat.widget.ActionMenuView
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView
io.flutter.embedding.engine.FlutterJNI
io.flutter.plugin.platform.SingleViewPresentation
androidx.appcompat.widget.Toolbar
androidx.media3.exoplayer.image.ImageOutput
androidx.preference.UnPressableLinearLayout
androidx.startup.InitializationProvider
android.support.v4.media.MediaBrowserCompat$SearchResultReceiver
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
android.support.v4.media.MediaDescriptionCompat
androidx.appcompat.widget.ActionBarOverlayLayout
android.support.v4.media.session.MediaControllerCompat$MediaControllerImplApi21$ExtraBinderRequestResultReceiver
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin
android.support.v4.media.MediaMetadataCompat
androidx.appcompat.widget.ActionBarContextView
com.example.iptv_player.MainActivity
androidx.appcompat.widget.AlertDialogLayout
androidx.media.AudioAttributesImplBase
androidx.media3.ui.PlayerView
androidx.recyclerview.widget.RecyclerView
kotlinx.coroutines.android.AndroidDispatcherFactory
androidx.window.extensions.core.util.function.Function
androidx.preference.SwitchPreferenceCompat
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
androidx.window.extensions.core.util.function.Consumer
androidx.core.graphics.drawable.IconCompat
io.flutter.plugin.text.ProcessTextPlugin
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
androidx.preference.PreferenceGroup
androidx.media.AudioAttributesImplApi21
androidx.appcompat.view.menu.ListMenuItemView
androidx.annotation.Keep
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
kotlin.coroutines.jvm.internal.BaseContinuationImpl
android.support.v4.app.RemoteActionCompatParcelizer
androidx.preference.EditTextPreference
android.support.v4.media.MediaBrowserCompat$MediaItem
androidx.preference.internal.PreferenceImageView
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
androidx.lifecycle.ProcessLifecycleInitializer
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
androidx.preference.CheckBoxPreference
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
androidx.media.AudioAttributesImplApi26Parcelizer
androidx.appcompat.widget.ContentFrameLayout
androidx.media3.exoplayer.dash.DashMediaSource$Factory
androidx.browser.browseractions.BrowserActionsFallbackMenuView
androidx.media3.exoplayer.ExoPlayer
io.flutter.plugins.urllauncher.WebViewActivity
io.flutter.view.TextureRegistry$ImageTextureEntry
android.support.v4.media.AudioAttributesImplApi26Parcelizer
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
androidx.recyclerview.widget.LinearLayoutManager
androidx.lifecycle.ProcessLifecycleOwner$attach$1
androidx.recyclerview.widget.StaggeredGridLayoutManager
io.flutter.plugins.videoplayer.VideoPlayerPlugin
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
io.flutter.view.TextureRegistry$SurfaceProducer
androidx.appcompat.app.AlertController$RecycleListView
android.support.v4.media.session.MediaSessionCompat$QueueItem
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
androidx.appcompat.view.menu.ExpandedMenuView
androidx.preference.DropDownPreference
androidx.media.AudioAttributesImplBaseParcelizer
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
android.support.v4.media.MediaBrowserCompat$ItemReceiver
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
androidx.preference.ListPreference
androidx.core.app.RemoteActionCompatParcelizer
androidx.appcompat.widget.DialogTitle
android.support.v4.media.session.MediaSessionCompat$Token
android.support.v4.media.AudioAttributesCompatParcelizer
com.tekartik.sqflite.SqflitePlugin
androidx.preference.Preference
androidx.preference.MultiSelectListPreference
kotlinx.coroutines.internal.StackTraceRecoveryKt
androidx.preference.SwitchPreference
androidx.core.widget.NestedScrollView
io.flutter.view.TextureRegistry$SurfaceTextureEntry
androidx.media3.exoplayer.hls.HlsMediaSource$Factory
android.support.v4.media.session.ParcelableVolumeInfo
androidx.preference.PreferenceScreen
android.support.v4.media.RatingCompat
androidx.preference.TwoStatePreference
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.appcompat.widget.SearchView$SearchAutoComplete
io.flutter.plugins.urllauncher.UrlLauncherPlugin
androidx.versionedparcelable.ParcelImpl
android.support.v4.media.AudioAttributesImplBaseParcelizer
androidx.recyclerview.widget.GridLayoutManager
androidx.media.AudioAttributesImplApi26
androidx.lifecycle.ReportFragment
androidx.appcompat.widget.FitWindowsLinearLayout
io.flutter.view.TextureRegistry$GLTextureConsumer
io.flutter.view.FlutterCallbackInformation
androidx.appcompat.widget.ActionBarContainer
android.support.v4.media.AudioAttributesImplApi21Parcelizer
androidx.profileinstaller.ProfileInstallerInitializer
com.example.iptv_player.ExoPlayerActivity
androidx.media.AudioAttributesImplApi21Parcelizer
androidx.appcompat.view.menu.ActionMenuItemView
androidx.media3.ui.SubtitleView
androidx.media.AudioAttributesCompat
android.support.v4.media.session.PlaybackStateCompat
android.support.v4.media.MediaBrowserCompat$CustomActionResultReceiver
androidx.media.AudioAttributesImpl
androidx.window.extensions.core.util.function.Predicate
androidx.versionedparcelable.CustomVersionedParcelable
io.flutter.plugins.GeneratedPluginRegistrant
androidx.preference.PreferenceCategory
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView
androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory
io.flutter.view.AccessibilityViewEmbedder
androidx.profileinstaller.ProfileInstallReceiver
androidx.media.AudioAttributesCompatParcelizer
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
android.support.v4.media.session.MediaSessionCompat$QueueItem: android.os.Parcelable$Creator CREATOR
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
io.flutter.embedding.engine.FlutterJNI: float displayWidth
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
kotlinx.coroutines.InvokeOnCancelling: int _invoked
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
androidx.media3.extractor.metadata.scte35.SpliceInsertCommand: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.id3.ChapterFrame: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage lastDequeuedImage
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
android.support.v4.media.MediaDescriptionCompat: android.os.Parcelable$Creator CREATOR
androidx.media3.common.DrmInitData$SchemeData: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.scte35.TimeSignalCommand: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: long receivers
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
androidx.media3.exoplayer.hls.HlsTrackMetadataEntry$VariantInfo: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
androidx.media3.extractor.metadata.id3.TextInformationFrame: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
kotlinx.coroutines.DispatchedCoroutine: int _decision
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
io.flutter.embedding.engine.FlutterOverlaySurface: int id
io.flutter.plugin.platform.SingleViewPresentation: int viewId
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
androidx.media3.extractor.metadata.id3.PrivFrame: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
android.support.v4.media.session.MediaSessionCompat$Token: android.os.Parcelable$Creator CREATOR
androidx.media3.common.StreamKey: android.os.Parcelable$Creator CREATOR
android.support.v4.media.session.ParcelableVolumeInfo: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.dvbsi.AppInfoTable: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.icy.IcyInfo: android.os.Parcelable$Creator CREATOR
androidx.media3.container.Mp4TimestampData: android.os.Parcelable$Creator CREATOR
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
androidx.media3.extractor.metadata.mp4.SlowMotionData: android.os.Parcelable$Creator CREATOR
android.support.v4.media.session.PlaybackStateCompat: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: float displayHeight
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
androidx.datastore.preferences.PreferencesProto$Value: int BYTES_FIELD_NUMBER
androidx.media3.extractor.metadata.scte35.SpliceScheduleCommand: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
kotlinx.coroutines.DefaultExecutor: int debugStatus
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
androidx.media3.extractor.metadata.scte35.SpliceNullCommand: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
androidx.media3.container.MdtaMetadataEntry: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
android.support.v4.media.MediaBrowserCompat$MediaItem: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
kotlinx.coroutines.CompletedExceptionally: int _handled
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
androidx.media3.extractor.metadata.mp4.SmtaMetadataEntry: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
androidx.media3.extractor.metadata.vorbis.VorbisComment: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
androidx.media3.common.DrmInitData: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
androidx.media3.extractor.metadata.icy.IcyHeaders: android.os.Parcelable$Creator CREATOR
android.support.v4.media.session.MediaSessionCompat$ResultReceiverWrapper: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
androidx.media3.container.Mp4LocationData: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.id3.MlltFrame: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
android.support.v4.media.RatingCompat: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
androidx.media3.extractor.metadata.id3.GeobFrame: android.os.Parcelable$Creator CREATOR
android.support.v4.media.session.PlaybackStateCompat$CustomAction: android.os.Parcelable$Creator CREATOR
androidx.media3.common.Metadata: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
androidx.media3.extractor.metadata.id3.InternalFrame: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
androidx.media3.extractor.metadata.flac.VorbisComment: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
androidx.media3.extractor.metadata.id3.UrlLinkFrame: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.CancelledContinuation: int _resumed
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
androidx.media3.exoplayer.hls.HlsTrackMetadataEntry: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.JobSupport: java.lang.Object _state
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
androidx.media3.extractor.metadata.id3.BinaryFrame: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.mp4.SlowMotionData$Segment: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
androidx.media3.extractor.metadata.id3.CommentFrame: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: float displayDensity
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
android.support.v4.os.ResultReceiver: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
android.support.v4.media.MediaMetadataCompat: android.os.Parcelable$Creator CREATOR
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
androidx.media3.extractor.metadata.scte35.PrivateCommand: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
androidx.media3.extractor.metadata.flac.PictureFrame: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.mp4.MotionPhotoMetadata: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
androidx.media3.extractor.metadata.emsg.EventMessage: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
androidx.media3.extractor.metadata.id3.ApicFrame: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
androidx.media3.extractor.metadata.id3.ChapterTocFrame: android.os.Parcelable$Creator CREATOR
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
androidx.appcompat.widget.SearchView: int getPreferredWidth()
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.media3.ui.SubtitleView: java.util.List getCuesWithStylingPreferencesApplied()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
androidx.media3.exoplayer.audio.MediaCodecAudioRenderer$Api23: void setAudioSinkPreferredDevice(androidx.media3.exoplayer.audio.AudioSink,java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.graphics.drawable.IconCompat: IconCompat()
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: void write(androidx.media.AudioAttributesImplApi26,androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
androidx.core.app.AppOpsManagerCompat$Api29Impl: android.app.AppOpsManager getSystemService(android.content.Context)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.media3.exoplayer.audio.DefaultAudioSink$Api23: void setPreferredDeviceOnAudioTrack(android.media.AudioTrack,androidx.media3.exoplayer.audio.AudioDeviceInfoApi23)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
com.google.common.collect.AbstractIterator$State: com.google.common.collect.AbstractIterator$State valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
androidx.appcompat.widget.Toolbar: void setLogo(int)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.media3.ui.PlayerControlView: int getShowTimeoutMs()
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: AudioAttributesImplApi26Parcelizer()
androidx.media.AudioAttributesCompatParcelizer: AudioAttributesCompatParcelizer()
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.media3.ui.PlayerView: void setShowFastForwardButton(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType: io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
androidx.media3.ui.PlayerView: android.graphics.drawable.Drawable getDefaultArtwork()
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.media3.ui.SubtitleView: void setApplyEmbeddedFontSizes(boolean)
io.flutter.plugins.urllauncher.UrlLauncherPlugin: UrlLauncherPlugin()
androidx.media3.exoplayer.ExoPlayerImpl$Api31: androidx.media3.exoplayer.analytics.PlayerId registerMediaMetricsListener(android.content.Context,androidx.media3.exoplayer.ExoPlayerImpl,boolean,java.lang.String)
androidx.browser.customtabs.CustomTabsIntent$Api24Impl: java.lang.String getDefaultLocale()
androidx.media.AudioAttributesImplApi26: AudioAttributesImplApi26()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.media3.ui.PlayerView: void setShowSubtitleButton(boolean)
androidx.media3.ui.DefaultTimeBar: void setPosition(long)
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
androidx.media.AudioAttributesImplBase: AudioAttributesImplBase()
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
androidx.media3.ui.DefaultTimeBar: void setKeyCountIncrement(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
androidx.media3.ui.PlayerView: void setResizeMode(int)
com.google.common.collect.Iterators$EmptyModifiableIterator: com.google.common.collect.Iterators$EmptyModifiableIterator[] values()
androidx.media3.ui.SubtitleView: void setApplyEmbeddedStyles(boolean)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.media3.exoplayer.audio.DefaultAudioSink$Api31: void setLogSessionIdOnAudioTrack(android.media.AudioTrack,androidx.media3.exoplayer.analytics.PlayerId)
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.appcompat.widget.Toolbar: void setTitle(int)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.media3.ui.PlayerControlView: boolean getShowShuffleButton()
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat: io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat[] values()
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
androidx.media.AudioAttributesImplBaseParcelizer: AudioAttributesImplBaseParcelizer()
io.flutter.plugins.urllauncher.WebViewActivity: WebViewActivity()
androidx.media3.ui.PlayerView: void setErrorMessageProvider(androidx.media3.common.ErrorMessageProvider)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setDescription(android.media.MediaDescription$Builder,java.lang.CharSequence)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
androidx.media3.ui.PlayerControlView: void setPlaybackSpeed(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.core.widget.NestedScrollView: int getScrollRange()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
androidx.media3.common.util.Util$Api21: android.graphics.drawable.Drawable getDrawable(android.content.Context,android.content.res.Resources,int)
androidx.appcompat.widget.SearchView: void setImeOptions(int)
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
io.flutter.embedding.android.FlutterView: void setVisibility(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
com.example.iptv_player.MainActivity: MainActivity()
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setExtras(android.media.MediaDescription$Builder,android.os.Bundle)
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
androidx.media3.ui.PlayerControlView: androidx.media3.common.Player getPlayer()
androidx.media.AudioAttributesImplApi26Parcelizer: androidx.media.AudioAttributesImplApi26 read(androidx.versionedparcelable.VersionedParcel)
androidx.media3.ui.PlayerControlView: void setShowPlayButtonIfPlaybackIsSuppressed(boolean)
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.media3.exoplayer.audio.DefaultAudioOffloadSupportProvider$Api31: androidx.media3.exoplayer.audio.AudioOffloadSupport getOffloadedPlaybackSupport(android.media.AudioFormat,android.media.AudioAttributes,boolean)
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
androidx.media3.ui.PlayerView: void setShowShuffleButton(boolean)
com.google.common.collect.Ordering: Ordering()
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOpNoThrow(android.app.AppOpsManager,java.lang.String,java.lang.String)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
androidx.media3.ui.SubtitleView: void setStyle(androidx.media3.ui.CaptionStyleCompat)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
com.google.common.base.AbstractIterator$State: com.google.common.base.AbstractIterator$State valueOf(java.lang.String)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory: SsMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
androidx.media3.ui.PlayerControlView: void setShowFastForwardButton(boolean)
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.example.iptv_player.ExoPlayerActivity: ExoPlayerActivity()
androidx.media3.ui.PlayerControlView: void setShowRewindButton(boolean)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
androidx.media3.ui.PlayerView: void setAspectRatioListener(androidx.media3.ui.AspectRatioFrameLayout$AspectRatioListener)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.media3.ui.PlayerView: androidx.media3.common.Player getPlayer()
androidx.media3.ui.SubtitleView: androidx.media3.ui.CaptionStyleCompat getUserCaptionStyle()
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.media3.ui.PlayerView: void setImage(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.media3.ui.PlayerControlView: void setShowVrButton(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
androidx.media3.ui.PlayerView: void setShowNextButton(boolean)
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
androidx.media3.common.AudioAttributes$Api29: void setAllowedCapturePolicy(android.media.AudioAttributes$Builder,int)
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.media3.ui.PlayerView: void setUseArtwork(boolean)
androidx.media3.exoplayer.drm.DrmUtil$Api21: boolean isMediaDrmStateException(java.lang.Throwable)
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
androidx.media3.exoplayer.mediacodec.MediaCodecPerformancePointCoverageProvider$Api29: int areResolutionAndFrameRateCovered(android.media.MediaCodecInfo$VideoCapabilities,int,int,double)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
androidx.media3.ui.TrackSelectionView: void setTrackNameProvider(androidx.media3.ui.TrackNameProvider)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
androidx.media3.ui.PlayerView: void setShowMultiWindowTimeBar(boolean)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$700(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
androidx.media3.ui.DefaultTimeBar: DefaultTimeBar(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory: RtspMediaSource$Factory()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
androidx.media3.exoplayer.audio.AudioCapabilities$Api23: boolean isBluetoothConnected(android.media.AudioManager,androidx.media3.exoplayer.audio.AudioDeviceInfoApi23)
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
androidx.media3.exoplayer.audio.AudioCapabilitiesReceiver$Api23: void unregisterAudioDeviceCallback(android.content.Context,android.media.AudioDeviceCallback)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.media3.ui.PlayerControlView: void setShowSubtitleButton(boolean)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
androidx.media3.exoplayer.dash.DashMediaSource$Factory: DashMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
com.google.common.collect.Iterators$EmptyModifiableIterator: com.google.common.collect.Iterators$EmptyModifiableIterator valueOf(java.lang.String)
androidx.media3.ui.AspectRatioFrameLayout: int getResizeMode()
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
androidx.media.AudioAttributesImplApi26Parcelizer: void write(androidx.media.AudioAttributesImplApi26,androidx.versionedparcelable.VersionedParcel)
android.support.v4.media.AudioAttributesCompatParcelizer: void write(androidx.media.AudioAttributesCompat,androidx.versionedparcelable.VersionedParcel)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.recyclerview.widget.RecyclerView: java.lang.CharSequence getAccessibilityClassName()
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.media3.exoplayer.ExoPlayerImpl$Api23: boolean isSuitableAudioOutputPresentInAudioDeviceInfoList(android.content.Context,android.media.AudioDeviceInfo[])
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.media3.ui.AspectRatioFrameLayout: void setAspectRatio(float)
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
androidx.media3.ui.PlayerControlView: void setShowTimeoutMs(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.media3.exoplayer.audio.AudioCapabilities$Api29: int getMaxSupportedChannelCountForPassthrough(int,int,androidx.media3.common.AudioAttributes)
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
androidx.appcompat.widget.SearchView: int getPreferredHeight()
androidx.media3.exoplayer.audio.DefaultAudioSink$StreamEventCallbackV29: void register(android.media.AudioTrack)
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setMediaId(android.media.MediaDescription$Builder,java.lang.String)
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat: io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat valueOf(java.lang.String)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
androidx.media3.exoplayer.video.MediaCodecVideoRenderer$Api26: boolean doesDisplaySupportDolbyVision(android.content.Context)
androidx.media3.ui.PlayerView$Api34: void setSurfaceLifecycleToFollowsAttachment(android.view.SurfaceView)
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
androidx.media.AudioAttributesImplApi21Parcelizer: void write(androidx.media.AudioAttributesImplApi21,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: void close(java.io.FileDescriptor)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.media3.ui.PlayerControlView: void setTimeBarMinUpdateInterval(int)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.media3.ui.PlayerView: PlayerView(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.media3.ui.PlayerControlView: void setShowPreviousButton(boolean)
androidx.media3.exoplayer.drm.FrameworkMediaDrm$Api31: void setLogSessionIdOnMediaDrmSession(android.media.MediaDrm,byte[],androidx.media3.exoplayer.analytics.PlayerId)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
androidx.media3.ui.DefaultTimeBar: long getScrubberPosition()
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
androidx.recyclerview.widget.RecyclerView: void setLayoutTransition(android.animation.LayoutTransition)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List)
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
androidx.media3.ui.DefaultTimeBar: void setKeyTimeIncrement(long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.media3.ui.PlayerView: boolean getUseArtwork()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
androidx.media3.exoplayer.image.ImageOutput: void onDisabled()
android.support.v4.media.AudioAttributesImplBaseParcelizer: androidx.media.AudioAttributesImplBase read(androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
androidx.media3.ui.PlayerView: void setControllerVisibilityListener(androidx.media3.ui.PlayerView$ControllerVisibilityListener)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: long lseek(java.io.FileDescriptor,long,int)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.media.MediaDescription$Builder createBuilder()
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.media3.ui.PlayerControlView: void setPlayer(androidx.media3.common.Player)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView: androidx.media3.exoplayer.video.VideoDecoderOutputBufferRenderer getVideoDecoderOutputBufferRenderer()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
androidx.media3.ui.PlayerView: void setRepeatToggleModes(int)
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
androidx.media3.ui.PlayerControlView: void setRepeatToggleModes(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.media3.ui.PlayerView: void setControllerOnFullScreenModeChangedListener(androidx.media3.ui.PlayerControlView$OnFullScreenModeChangedListener)
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
androidx.media3.ui.DefaultTimeBar: void setUnplayedColor(int)
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
androidx.media3.ui.TrackSelectionView: void setShowDisableOption(boolean)
com.google.common.collect.BaseImmutableMultimap: BaseImmutableMultimap()
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView: void setOutputBuffer(androidx.media3.decoder.VideoDecoderOutputBuffer)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
android.support.v4.media.MediaDescriptionCompat$Api23Impl: void setMediaUri(android.media.MediaDescription$Builder,android.net.Uri)
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
androidx.media3.ui.PlayerView: void setDefaultArtwork(android.graphics.drawable.Drawable)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
androidx.media3.ui.AspectRatioFrameLayout: void setAspectRatioListener(androidx.media3.ui.AspectRatioFrameLayout$AspectRatioListener)
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: void setUseSensorRotation(boolean)
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.media3.ui.PlayerView: void setShutterBackgroundColor(int)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
androidx.media3.ui.PlayerView: android.view.View getVideoSurfaceView()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.core.app.AppOpsManagerCompat$Api29Impl: java.lang.String getOpPackageName(android.content.Context)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.browser.browseractions.BrowserActionsFallbackMenuView: BrowserActionsFallbackMenuView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
androidx.core.content.ContextCompat$Api26Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
androidx.media3.ui.PlayerView: void setShowBuffering(int)
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
androidx.media3.exoplayer.video.VideoFrameReleaseHelper$Api30: void setSurfaceFrameRate(android.view.Surface,float)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.SearchView: int getInputType()
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
androidx.media3.ui.PlayerView: android.widget.FrameLayout getOverlayFrameLayout()
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView$Adapter$StateRestorationPolicy: androidx.recyclerview.widget.RecyclerView$Adapter$StateRestorationPolicy[] values()
androidx.media3.ui.PlayerControlView: void setShowShuffleButton(boolean)
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: androidx.media3.exoplayer.video.VideoFrameMetadataListener getVideoFrameMetadataListener()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: SphericalGLSurfaceView(android.content.Context)
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
androidx.media3.ui.PlayerView: int getImageDisplayMode()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType: io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType[] values()
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.net.Uri getIconUri(android.media.MediaDescription)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
androidx.recyclerview.widget.RecyclerView: int getBaseline()
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
androidx.media.AudioAttributesCompatParcelizer: void write(androidx.media.AudioAttributesCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: void write(androidx.media.AudioAttributesImplApi21,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.String permissionToOp(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
android.support.v4.media.AudioAttributesCompatParcelizer: androidx.media.AudioAttributesCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.media3.ui.PlayerControlView: void setOnFullScreenModeChangedListener(androidx.media3.ui.PlayerControlView$OnFullScreenModeChangedListener)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView: VideoDecoderGLSurfaceView(android.content.Context)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setIconUri(android.media.MediaDescription$Builder,android.net.Uri)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
com.google.common.base.Function: java.lang.Object apply(java.lang.Object)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
androidx.media3.ui.PlayerControlView: boolean getShowVrButton()
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
androidx.media3.ui.PlayerView: void setPlayer(androidx.media3.common.Player)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.content.ContextCompat$Api26Impl: android.content.ComponentName startForegroundService(android.content.Context,android.content.Intent)
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.media.AudioAttributesCompatParcelizer: androidx.media.AudioAttributesCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
androidx.media3.ui.SubtitleView: void setView(android.view.View)
androidx.startup.InitializationProvider: InitializationProvider()
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.graphics.Bitmap getIconBitmap(android.media.MediaDescription)
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
android.support.v4.media.MediaDescriptionCompat$Api23Impl: android.net.Uri getMediaUri(android.media.MediaDescription)
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.media3.ui.DefaultTimeBar: void setPlayedAdMarkerColor(int)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
androidx.media.AudioAttributesCompat: AudioAttributesCompat()
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
androidx.media3.ui.PlayerView: void setArtworkDisplayMode(int)
androidx.media3.exoplayer.audio.DefaultAudioSink$StreamEventCallbackV29: void unregister(android.media.AudioTrack)
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees: io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: AudioAttributesImplApi21Parcelizer()
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees: io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
androidx.media3.exoplayer.audio.DefaultAudioOffloadSupportProvider$Api29: androidx.media3.exoplayer.audio.AudioOffloadSupport getOffloadedPlaybackSupport(android.media.AudioFormat,android.media.AudioAttributes,boolean)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
androidx.media3.ui.PlayerView$SurfaceSyncGroupCompatV34: void postRegister(android.os.Handler,android.view.SurfaceView,java.lang.Runnable)
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
androidx.appcompat.widget.SearchView: void setIconified(boolean)
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
androidx.media3.exoplayer.audio.AudioCapabilities$Api33: androidx.media3.exoplayer.audio.AudioDeviceInfoApi23 getDefaultRoutedDeviceForAttributes(android.media.AudioManager,androidx.media3.common.AudioAttributes)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setTitle(android.media.MediaDescription$Builder,java.lang.CharSequence)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.media3.ui.SubtitleView: void setFractionalTextSize(float)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.media3.ui.AspectRatioFrameLayout: AspectRatioFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
androidx.media3.ui.PlayerView: void setControllerShowTimeoutMs(int)
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.CharSequence getTitle(android.media.MediaDescription)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
androidx.media3.ui.PlayerView: java.util.List getAdOverlayInfos()
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
androidx.media3.ui.DefaultTimeBar: void setPlayedColor(int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
androidx.media3.ui.PlayerView$SurfaceSyncGroupCompatV34: void maybeMarkSyncReadyAndClear()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
androidx.media3.ui.PlayerView: void setFullscreenButtonClickListener(androidx.media3.ui.PlayerView$FullscreenButtonClickListener)
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.media3.ui.PlayerView: void setControllerVisibilityListener(androidx.media3.ui.PlayerControlView$VisibilityListener)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
io.flutter.plugins.videoplayer.VideoPlayerPlugin: VideoPlayerPlugin()
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
androidx.recyclerview.widget.RecyclerView: void suppressLayout(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
androidx.media.AudioAttributesImplApi21: AudioAttributesImplApi21()
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOp(android.app.AppOpsManager,java.lang.String,java.lang.String)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.media3.ui.PlayerView: void setShowRewindButton(boolean)
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: android.view.Surface getVideoSurface()
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
androidx.media3.exoplayer.ExoPlayerImpl$Api23: void registerAudioDeviceCallback(android.media.AudioManager,android.media.AudioDeviceCallback,android.os.Handler)
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
androidx.media3.ui.PlayerView: void setKeepContentOnPlayerReset(boolean)
androidx.media3.ui.PlayerView: int getResizeMode()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
androidx.media3.ui.DefaultTimeBar: void setScrubberColor(int)
androidx.media3.ui.PlayerView: void setUseController(boolean)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
androidx.media3.ui.PlayerView: void setControllerHideOnTouch(boolean)
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
kotlin.collections.AbstractList: AbstractList()
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
androidx.media3.common.AudioAttributes$Api32: void setSpatializationBehavior(android.media.AudioAttributes$Builder,int)
androidx.core.content.ContextCompat$Api33Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.media3.ui.PlayerView: int getArtworkDisplayMode()
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.media3.ui.PlayerView: void setShowPlayButtonIfPlaybackIsSuppressed(boolean)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
com.google.common.collect.AbstractMapEntry: AbstractMapEntry()
androidx.browser.customtabs.CustomTabsIntent$Api34Impl: void setShareIdentityEnabled(android.app.ActivityOptions,boolean)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
androidx.media3.ui.TrackSelectionView: TrackSelectionView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin: WakelockPlusPlugin()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.media3.ui.SubtitleView: void setCues(java.util.List)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.media3.ui.SubtitleView: void setBottomPaddingFraction(float)
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: androidx.media3.exoplayer.video.spherical.CameraMotionListener getCameraMotionListener()
androidx.media3.exoplayer.audio.DefaultAudioSink$OnRoutingChangedListenerApi24: void release()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
androidx.media3.ui.PlayerControlView: boolean getShowSubtitleButton()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
androidx.media3.exoplayer.audio.AudioCapabilitiesReceiver$Api23: void registerAudioDeviceCallback(android.content.Context,android.media.AudioDeviceCallback,android.os.Handler)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
androidx.media3.ui.PlayerView: int getControllerShowTimeoutMs()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.os.Bundle getExtras(android.media.MediaDescription)
androidx.media3.ui.TrackSelectionView: boolean getIsDisabled()
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
androidx.media3.ui.PlayerView: boolean getUseController()
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setSubtitle(android.media.MediaDescription$Builder,java.lang.CharSequence)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
androidx.lifecycle.ReportFragment: ReportFragment()
androidx.media3.exoplayer.drm.DrmUtil$Api23: boolean isMediaDrmResetException(java.lang.Throwable)
androidx.media.AudioAttributesImplApi26Parcelizer: AudioAttributesImplApi26Parcelizer()
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
androidx.media.AudioAttributesImplApi21Parcelizer: AudioAttributesImplApi21Parcelizer()
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.media3.ui.DefaultTimeBar: void setAdMarkerColor(int)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
androidx.media3.ui.PlayerControlView: void setShowNextButton(boolean)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
androidx.media3.ui.TrackSelectionView: void setAllowMultipleOverrides(boolean)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
androidx.media3.exoplayer.audio.AudioCapabilities$Api29: com.google.common.collect.ImmutableList getDirectPlaybackSupportedEncodings(androidx.media3.common.AudioAttributes)
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: java.io.FileDescriptor dup(java.io.FileDescriptor)
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction[] values()
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
androidx.media3.ui.AspectRatioFrameLayout: void setResizeMode(int)
androidx.media3.ui.SubtitleView: float getUserCaptionFontScale()
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
androidx.media3.ui.PlayerView: void setImageDisplayMode(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void lambda$dequeueImage$0()
com.google.common.util.concurrent.DirectExecutor: com.google.common.util.concurrent.DirectExecutor valueOf(java.lang.String)
androidx.media3.datasource.RawResourceDataSource: android.net.Uri buildRawResourceUri(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
android.support.v4.media.AudioAttributesImplApi21Parcelizer: androidx.media.AudioAttributesImplApi21 read(androidx.versionedparcelable.VersionedParcel)
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
androidx.media3.ui.DefaultTimeBar: void setDuration(long)
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$Api31: void setLogSessionIdToMediaCodecFormat(androidx.media3.exoplayer.mediacodec.MediaCodecAdapter$Configuration,androidx.media3.exoplayer.analytics.PlayerId)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
androidx.media3.datasource.FileDataSource$Api21: boolean isPermissionError(java.lang.Throwable)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
com.google.common.collect.AbstractMultimap: AbstractMultimap()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
androidx.recyclerview.widget.RecyclerView: boolean isLayoutSuppressed()
androidx.media3.ui.DefaultTimeBar: void setEnabled(boolean)
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
androidx.media3.ui.PlayerControlView: void setAnimationEnabled(boolean)
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.media3.ui.PlayerView: void setControllerAutoShow(boolean)
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.media3.ui.PlayerView: boolean getControllerHideOnTouch()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.media3.exoplayer.ExoPlayer: void setImageOutput(androidx.media3.exoplayer.image.ImageOutput)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
androidx.media3.ui.PlayerView: void setCustomErrorMessage(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
androidx.media3.ui.DefaultTimeBar: void setBufferedPosition(long)
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin: PackageInfoPlugin()
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.String getMediaId(android.media.MediaDescription)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.media3.ui.SubtitleView: void setViewType(int)
androidx.media3.ui.DefaultTimeBar: long getPreferredUpdateDelay()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
android.support.v4.media.AudioAttributesImplBaseParcelizer: void write(androidx.media.AudioAttributesImplBase,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
androidx.exifinterface.media.ExifInterfaceUtils$Api23Impl: void setDataSource(android.media.MediaMetadataRetriever,android.media.MediaDataSource)
androidx.media3.ui.DefaultTimeBar: long getPositionIncrement()
androidx.media3.ui.PlayerControlView: int getRepeatToggleModes()
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.appcompat.widget.SearchView: int getImeOptions()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
com.google.common.util.concurrent.DirectExecutor: com.google.common.util.concurrent.DirectExecutor[] values()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
com.tekartik.sqflite.SqflitePlugin: SqflitePlugin()
androidx.media3.ui.SubtitleView: SubtitleView(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setIconBitmap(android.media.MediaDescription$Builder,android.graphics.Bitmap)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.media3.exoplayer.audio.DefaultAudioSink$OnRoutingChangedListenerApi24: void onRoutingChanged(android.media.AudioRouting)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType[] values()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.media3.ui.PlayerView: void setShowVrButton(boolean)
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
androidx.media3.ui.PlayerView: boolean getControllerAutoShow()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
androidx.media3.exoplayer.audio.AudioCapabilities$Api23: com.google.common.collect.ImmutableSet getAllBluetoothDeviceTypes()
androidx.media3.exoplayer.drm.DrmUtil$Api21: int mediaDrmStateExceptionToErrorCode(java.lang.Throwable)
androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$Api21: boolean registerOnBufferAvailableListener(androidx.media3.exoplayer.mediacodec.MediaCodecAdapter,androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$MediaCodecRendererCodecAdapterListener)
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
android.support.v4.media.AudioAttributesImplBaseParcelizer: AudioAttributesImplBaseParcelizer()
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
androidx.media3.ui.TrackSelectionView: void setAllowAdaptiveSelections(boolean)
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
androidx.browser.customtabs.CustomTabsIntent$Api23Impl: android.app.ActivityOptions makeBasicActivityOptions()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.CharSequence getSubtitle(android.media.MediaDescription)
android.support.v4.media.AudioAttributesCompatParcelizer: AudioAttributesCompatParcelizer()
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.SearchView: void setInputType(int)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.app.AppOpsManagerCompat$Api29Impl: int checkOpNoThrow(android.app.AppOpsManager,java.lang.String,int,java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
androidx.media3.exoplayer.audio.AudioCapabilities$Api33: androidx.media3.exoplayer.audio.AudioCapabilities getCapabilitiesInternalForDirectPlayback(android.media.AudioManager,androidx.media3.common.AudioAttributes)
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
androidx.media.AudioAttributesImplApi21Parcelizer: androidx.media.AudioAttributesImplApi21 read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.media3.ui.PlayerControlView: void setShowMultiWindowTimeBar(boolean)
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.CharSequence getDescription(android.media.MediaDescription)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
androidx.media3.ui.PlayerView: void setControllerAnimationEnabled(boolean)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
androidx.media3.ui.PlayerView: android.view.ViewGroup getAdViewGroup()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.media3.ui.PlayerControlView: void setVrButtonListener(android.view.View$OnClickListener)
kotlin.random.Random: Random()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
androidx.media3.exoplayer.image.ImageOutput: void onImageAvailable(long,android.graphics.Bitmap)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: androidx.media.AudioAttributesImplApi26 read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
androidx.media3.ui.PlayerView: androidx.media3.ui.SubtitleView getSubtitleView()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
androidx.media3.ui.PlayerControlView: void setProgressUpdateListener(androidx.media3.ui.PlayerControlView$ProgressUpdateListener)
androidx.media3.ui.PlayerView: void setControllerHideDuringAds(boolean)
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
androidx.recyclerview.widget.RecyclerView$Adapter$StateRestorationPolicy: androidx.recyclerview.widget.RecyclerView$Adapter$StateRestorationPolicy valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
com.google.common.base.Function: boolean equals(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView: void setDefaultStereoMode(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
androidx.media3.ui.DefaultTimeBar: void setBufferedColor(int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
androidx.media3.ui.PlayerView: void setVisibility(int)
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
androidx.media.AudioAttributesImplBaseParcelizer: void write(androidx.media.AudioAttributesImplBase,androidx.versionedparcelable.VersionedParcel)
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
androidx.appcompat.widget.SearchView: int getMaxWidth()
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
androidx.media.AudioAttributesImplBaseParcelizer: androidx.media.AudioAttributesImplBase read(androidx.versionedparcelable.VersionedParcel)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
com.google.common.base.AbstractIterator$State: com.google.common.base.AbstractIterator$State[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
androidx.media3.ui.PlayerView: void setImageOutput(androidx.media3.common.Player)
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.media.MediaDescription build(android.media.MediaDescription$Builder)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.media3.exoplayer.drm.FrameworkMediaDrm$Api31: boolean requiresSecureDecoder(android.media.MediaDrm,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
androidx.media3.ui.TrackSelectionView: java.util.Map getOverrides()
androidx.media3.exoplayer.hls.HlsMediaSource$Factory: HlsMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.media3.ui.PlayerView: void setShowPreviousButton(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
androidx.media3.ui.DefaultTimeBar: java.lang.String getProgressText()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
com.google.common.collect.AbstractIterator$State: com.google.common.collect.AbstractIterator$State[] values()
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
