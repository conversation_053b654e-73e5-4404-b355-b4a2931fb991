#Fri Jul 18 23:01:46 EET 2025
com.example.iptv_player.app-main-0\:/color/button_tint_selector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\color\\button_tint_selector.xml
com.example.iptv_player.app-main-0\:/drawable-v21/launch_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-v21\\launch_background.xml
com.example.iptv_player.app-main-0\:/drawable/button_selector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_selector.xml
com.example.iptv_player.app-main-0\:/drawable/gradient_bottom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\gradient_bottom.xml
com.example.iptv_player.app-main-0\:/drawable/gradient_top.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\gradient_top.xml
com.example.iptv_player.app-main-0\:/drawable/ic_arrow_back.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_arrow_back.xml
com.example.iptv_player.app-main-0\:/drawable/ic_audiotrack.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_audiotrack.xml
com.example.iptv_player.app-main-0\:/drawable/ic_forward_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_forward_10.xml
com.example.iptv_player.app-main-0\:/drawable/ic_fullscreen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_fullscreen.xml
com.example.iptv_player.app-main-0\:/drawable/ic_hd.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_hd.xml
com.example.iptv_player.app-main-0\:/drawable/ic_replay_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_replay_10.xml
com.example.iptv_player.app-main-0\:/drawable/ic_settings.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_settings.xml
com.example.iptv_player.app-main-0\:/drawable/ic_skip_next.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_skip_next.xml
com.example.iptv_player.app-main-0\:/drawable/ic_skip_previous.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_skip_previous.xml
com.example.iptv_player.app-main-0\:/drawable/ic_subtitles.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_subtitles.xml
com.example.iptv_player.app-main-0\:/drawable/ic_volume_up.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_volume_up.xml
com.example.iptv_player.app-main-0\:/drawable/launch_logo.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\launch_logo.xml
com.example.iptv_player.app-main-0\:/drawable/launch_logo_vector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\launch_logo_vector.xml
com.example.iptv_player.app-main-0\:/drawable/loading_dots.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\loading_dots.xml
com.example.iptv_player.app-main-0\:/drawable/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\logo.png
com.example.iptv_player.app-main-0\:/drawable/logo_small.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\logo_small.xml
com.example.iptv_player.app-main-0\:/drawable/logo_splash.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\logo_splash.xml
com.example.iptv_player.app-main-0\:/drawable/splash_screen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\splash_screen.xml
com.example.iptv_player.app-main-0\:/layout/activity_exoplayer.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_exoplayer.xml
com.example.iptv_player.app-main-0\:/layout/custom_player_control_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\custom_player_control_view.xml
com.example.iptv_player.app-main-0\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-0\:/mipmap-hdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\logo.png
com.example.iptv_player.app-main-0\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-0\:/mipmap-mdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\logo.png
com.example.iptv_player.app-main-0\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-0\:/mipmap-xhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\logo.png
com.example.iptv_player.app-main-0\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-0\:/mipmap-xxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\logo.png
com.example.iptv_player.app-main-0\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher.png
com.example.iptv_player.app-main-0\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\logo.png
com.example.iptv_player.app-main-0\:/xml/network_security_config.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\network_security_config.xml
