import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DirectionalFocusWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final bool autofocus;
  final String? debugLabel;
  final BorderRadius? borderRadius;
  final EdgeInsets? padding;
  final FocusNode? focusNode;

  const DirectionalFocusWidget({
    Key? key,
    required this.child,
    this.onPressed,
    this.autofocus = false,
    this.debugLabel,
    this.borderRadius,
    this.padding,
    this.focusNode,
  }) : super(key: key);

  @override
  State<DirectionalFocusWidget> createState() => _DirectionalFocusWidgetState();
}

class _DirectionalFocusWidgetState extends State<DirectionalFocusWidget>
    with SingleTickerProviderStateMixin {
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode(debugLabel: widget.debugLabel);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });

    if (_isFocused) {
      _animationController.forward();
      HapticFeedback.selectionClick();
      print("Focus gained: ${widget.debugLabel ?? 'Unknown'}");
    } else {
      _animationController.reverse();
      print("Focus lost: ${widget.debugLabel ?? 'Unknown'}");
    }
  }

  void _handlePress() {
    HapticFeedback.lightImpact();
    print("Pressed: ${widget.debugLabel ?? 'Unknown'}");
    widget.onPressed?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          print("Key in ${widget.debugLabel}: ${event.logicalKey}");
          
          // Handle activation keys
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space ||
              event.logicalKey == LogicalKeyboardKey.gameButtonA) {
            _handlePress();
            return KeyEventResult.handled;
          }
          
          // Handle directional navigation
          if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
            print("Moving focus right from ${widget.debugLabel}");
            _focusNode.nextFocus();
            return KeyEventResult.handled;
          }
          
          if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
            print("Moving focus left from ${widget.debugLabel}");
            _focusNode.previousFocus();
            return KeyEventResult.handled;
          }
          
          if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
            print("Moving focus down from ${widget.debugLabel}");
            _focusNode.focusInDirection(TraversalDirection.down);
            return KeyEventResult.handled;
          }
          
          if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
            print("Moving focus up from ${widget.debugLabel}");
            _focusNode.focusInDirection(TraversalDirection.up);
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: _handlePress,
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _isFocused ? Colors.white : Colors.transparent,
                    width: 1,
                  ),
                  borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
                  boxShadow: _isFocused ? [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.15),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ] : null,
                ),
                child: widget.child,
              ),
            );
          },
        ),
      ),
    );
  }
}
