#Fri Jul 18 23:01:46 EET 2025
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_fade_in.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_fade_in.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_fade_out.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_fade_out.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_grow_fade_in_from_bottom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_grow_fade_in_from_bottom.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_popup_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_popup_enter.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_popup_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_popup_exit.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_shrink_fade_out_from_bottom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_shrink_fade_out_from_bottom.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_slide_in_bottom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_slide_in_bottom.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_slide_in_top.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_slide_in_top.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_slide_out_bottom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_slide_out_bottom.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_slide_out_top.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_slide_out_top.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_tooltip_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_tooltip_enter.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/abc_tooltip_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_tooltip_exit.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_checkbox_to_checked_box_inner_merged_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_checked_box_inner_merged_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_checkbox_to_checked_box_outer_merged_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_checked_box_outer_merged_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_checkbox_to_checked_icon_null_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_checked_icon_null_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_checkbox_to_unchecked_box_inner_merged_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_unchecked_box_inner_merged_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_checkbox_to_unchecked_check_path_merged_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_unchecked_check_path_merged_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_checkbox_to_unchecked_icon_null_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_unchecked_icon_null_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_radio_to_off_mtrl_dot_group_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_off_mtrl_dot_group_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_radio_to_off_mtrl_ring_outer_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_off_mtrl_ring_outer_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_radio_to_off_mtrl_ring_outer_path_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_off_mtrl_ring_outer_path_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_radio_to_on_mtrl_dot_group_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_on_mtrl_dot_group_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_radio_to_on_mtrl_ring_outer_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_on_mtrl_ring_outer_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/anim/btn_radio_to_on_mtrl_ring_outer_path_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_on_mtrl_ring_outer_path_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v21/abc_btn_colored_borderless_text_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v21_abc_btn_colored_borderless_text_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v23/abc_btn_colored_borderless_text_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_btn_colored_borderless_text_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v23/abc_btn_colored_text_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_btn_colored_text_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v23/abc_color_highlight_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_color_highlight_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v23/abc_tint_btn_checkable.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_btn_checkable.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v23/abc_tint_default.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_default.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v23/abc_tint_edittext.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_edittext.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v23/abc_tint_seek_thumb.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_seek_thumb.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v23/abc_tint_spinner.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_spinner.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color-v23/abc_tint_switch_track.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_switch_track.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_background_cache_hint_selector_material_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_background_cache_hint_selector_material_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_background_cache_hint_selector_material_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_background_cache_hint_selector_material_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_btn_colored_text_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_btn_colored_text_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_hint_foreground_material_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_hint_foreground_material_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_hint_foreground_material_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_hint_foreground_material_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_primary_text_disable_only_material_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_primary_text_disable_only_material_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_primary_text_disable_only_material_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_primary_text_disable_only_material_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_primary_text_material_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_primary_text_material_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_primary_text_material_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_primary_text_material_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_search_url_text.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_search_url_text.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_secondary_text_material_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_secondary_text_material_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_secondary_text_material_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_secondary_text_material_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_tint_btn_checkable.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_tint_btn_checkable.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_tint_default.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_tint_default.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_tint_edittext.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_tint_edittext.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_tint_seek_thumb.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_tint_seek_thumb.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_tint_spinner.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_tint_spinner.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/abc_tint_switch_track.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_tint_switch_track.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/switch_thumb_material_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_switch_thumb_material_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/color/switch_thumb_material_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_switch_thumb_material_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_menu_paste_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_menu_selectall_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_menu_share_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_star_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_star_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_star_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_star_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_star_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_star_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_star_half_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_star_half_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_star_half_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_star_half_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_ic_star_half_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_star_half_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_list_focused_holo.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_focused_holo.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_list_longpressed_holo.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_longpressed_holo.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_pressed_holo_dark.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_pressed_holo_light.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_popup_background_mtrl_mult.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_text_select_handle_left_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_text_select_handle_left_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_text_select_handle_middle_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_text_select_handle_middle_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_text_select_handle_middle_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_text_select_handle_middle_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_text_select_handle_right_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_text_select_handle_right_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-hdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-hdpi-v17/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-hdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-hdpi-v17/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-hdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-hdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-hdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-mdpi-v17/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-mdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-mdpi-v17/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-mdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-mdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-mdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-xhdpi-v17/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xhdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-xhdpi-v17/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xhdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-xhdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xhdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-xxhdpi-v17/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxhdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-xxhdpi-v17/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxhdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-xxhdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxhdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-xxxhdpi-v17/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxxhdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-xxxhdpi-v17/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxxhdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-ldrtl-xxxhdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxxhdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_menu_paste_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_menu_selectall_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_menu_share_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_star_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_star_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_star_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_star_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_star_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_star_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_star_half_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_star_half_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_star_half_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_star_half_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_ic_star_half_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_star_half_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_list_focused_holo.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_focused_holo.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_list_longpressed_holo.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_longpressed_holo.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_pressed_holo_dark.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_pressed_holo_light.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_popup_background_mtrl_mult.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_text_select_handle_left_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_text_select_handle_left_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_text_select_handle_middle_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_text_select_handle_middle_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_text_select_handle_middle_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_text_select_handle_middle_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_text_select_handle_right_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_text_select_handle_right_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-mdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-v21/abc_action_bar_item_background_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_action_bar_item_background_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-v21/abc_btn_colored_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_btn_colored_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-v21/abc_dialog_material_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_dialog_material_background.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-v21/abc_edit_text_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_edit_text_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-v21/abc_list_divider_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_list_divider_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-v23/abc_control_background_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23_abc_control_background_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-watch-v20/abc_dialog_material_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-watch-v20_abc_dialog_material_background.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_menu_paste_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_menu_selectall_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_menu_share_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_star_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_star_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_star_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_star_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_star_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_star_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_star_half_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_star_half_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_star_half_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_star_half_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_ic_star_half_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_star_half_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_list_focused_holo.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_focused_holo.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_list_longpressed_holo.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_longpressed_holo.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_pressed_holo_dark.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_pressed_holo_light.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_popup_background_mtrl_mult.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_text_select_handle_left_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_text_select_handle_left_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_text_select_handle_middle_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_text_select_handle_middle_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_text_select_handle_middle_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_text_select_handle_middle_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_text_select_handle_right_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_text_select_handle_right_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xhdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_menu_paste_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_menu_selectall_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_menu_share_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_star_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_star_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_star_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_star_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_star_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_star_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_star_half_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_star_half_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_star_half_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_star_half_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_ic_star_half_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_star_half_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_list_focused_holo.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_focused_holo.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_list_longpressed_holo.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_longpressed_holo.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_pressed_holo_dark.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_pressed_holo_light.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_popup_background_mtrl_mult.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_text_select_handle_left_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_text_select_handle_left_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_text_select_handle_middle_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_text_select_handle_middle_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_text_select_handle_middle_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_text_select_handle_middle_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_text_select_handle_right_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_text_select_handle_right_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxhdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_menu_copy_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_menu_cut_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_menu_paste_mtrl_am_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_menu_selectall_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_menu_share_mtrl_alpha.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_star_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_star_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_star_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_star_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_star_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_star_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_star_half_black_16dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_star_half_black_16dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_star_half_black_36dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_star_half_black_36dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_ic_star_half_black_48dp.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_ic_star_half_black_48dp.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_text_select_handle_left_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_text_select_handle_left_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_text_select_handle_right_mtrl_dark.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable-xxxhdpi-v4/abc_text_select_handle_right_mtrl_light.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_btn_borderless_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_borderless_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_btn_check_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_check_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_btn_check_material_anim.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_check_material_anim.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_btn_default_mtrl_shape.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_default_mtrl_shape.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_btn_radio_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_radio_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_btn_radio_material_anim.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_radio_material_anim.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_cab_background_internal_bg.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_cab_background_internal_bg.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_cab_background_top_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_cab_background_top_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ic_ab_back_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_ab_back_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ic_arrow_drop_right_black_24dp.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_arrow_drop_right_black_24dp.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ic_clear_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_clear_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ic_go_search_api_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_go_search_api_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ic_menu_overflow_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_menu_overflow_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ic_search_api_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_search_api_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ic_voice_search_api_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_voice_search_api_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_item_background_holo_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_item_background_holo_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_item_background_holo_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_item_background_holo_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_list_selector_background_transition_holo_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_list_selector_background_transition_holo_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_list_selector_background_transition_holo_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_list_selector_background_transition_holo_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_list_selector_holo_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_list_selector_holo_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_list_selector_holo_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_list_selector_holo_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ratingbar_indicator_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ratingbar_indicator_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ratingbar_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ratingbar_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_ratingbar_small_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ratingbar_small_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_seekbar_thumb_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_seekbar_thumb_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_seekbar_tick_mark_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_seekbar_tick_mark_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_seekbar_track_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_seekbar_track_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_spinner_textfield_background_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_spinner_textfield_background_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_switch_thumb_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_switch_thumb_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_tab_indicator_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_tab_indicator_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_text_cursor_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_text_cursor_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/abc_textfield_search_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_textfield_search_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/btn_checkbox_checked_mtrl.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_checkbox_checked_mtrl.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_checkbox_checked_to_unchecked_mtrl_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/btn_checkbox_unchecked_mtrl.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_checkbox_unchecked_mtrl.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_checkbox_unchecked_to_checked_mtrl_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/btn_radio_off_mtrl.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_radio_off_mtrl.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/btn_radio_off_to_on_mtrl_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_radio_off_to_on_mtrl_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/btn_radio_on_mtrl.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_radio_on_mtrl.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/btn_radio_on_to_off_mtrl_animation.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_radio_on_to_off_mtrl_animation.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/tooltip_frame_dark.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_tooltip_frame_dark.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/drawable/tooltip_frame_light.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_tooltip_frame_light.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_checkbox_checked_mtrl_animation_interpolator_0.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_checkbox_checked_mtrl_animation_interpolator_1.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_radio_to_off_mtrl_animation_interpolator_0.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_radio_to_on_mtrl_animation_interpolator_0.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/interpolator/fast_out_slow_in.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_fast_out_slow_in.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout-v26/abc_screen_toolbar.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v26_abc_screen_toolbar.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout-watch-v20/abc_alert_dialog_button_bar_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-watch-v20_abc_alert_dialog_button_bar_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout-watch-v20/abc_alert_dialog_title_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-watch-v20_abc_alert_dialog_title_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_action_bar_title_item.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_bar_title_item.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_action_bar_up_container.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_bar_up_container.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_action_menu_item_layout.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_menu_item_layout.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_action_menu_layout.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_menu_layout.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_action_mode_bar.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_mode_bar.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_action_mode_close_item_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_mode_close_item_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_activity_chooser_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_activity_chooser_view.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_activity_chooser_view_list_item.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_activity_chooser_view_list_item.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_alert_dialog_button_bar_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_alert_dialog_button_bar_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_alert_dialog_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_alert_dialog_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_alert_dialog_title_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_alert_dialog_title_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_cascading_menu_item_layout.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_cascading_menu_item_layout.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_dialog_title_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_dialog_title_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_expanded_menu_layout.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_expanded_menu_layout.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_list_menu_item_checkbox.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_list_menu_item_checkbox.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_list_menu_item_icon.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_list_menu_item_icon.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_list_menu_item_layout.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_list_menu_item_layout.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_list_menu_item_radio.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_list_menu_item_radio.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_popup_menu_header_item_layout.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_popup_menu_header_item_layout.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_popup_menu_item_layout.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_popup_menu_item_layout.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_screen_content_include.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_screen_content_include.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_screen_simple.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_screen_simple.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_screen_simple_overlay_action_mode.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_screen_simple_overlay_action_mode.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_screen_toolbar.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_screen_toolbar.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_search_dropdown_item_icons_2line.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_search_dropdown_item_icons_2line.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_search_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_search_view.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_select_dialog_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_select_dialog_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/abc_tooltip.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_tooltip.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/select_dialog_item_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_select_dialog_item_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/select_dialog_multichoice_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_select_dialog_multichoice_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/select_dialog_singlechoice_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_select_dialog_singlechoice_material.xml.flat
com.example.iptv_player.app-appcompat-1.1.0-12\:/layout/support_simple_spinner_dropdown_item.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_support_simple_spinner_dropdown_item.xml.flat
com.example.iptv_player.app-browser-1.8.0-10\:/layout/browser_actions_context_menu_page.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_browser_actions_context_menu_page.xml.flat
com.example.iptv_player.app-browser-1.8.0-10\:/layout/browser_actions_context_menu_row.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_browser_actions_context_menu_row.xml.flat
com.example.iptv_player.app-browser-1.8.0-10\:/xml/image_share_filepaths.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_image_share_filepaths.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-anydpi-v21/ic_call_answer.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_answer.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-anydpi-v21/ic_call_answer_low.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_answer_low.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-anydpi-v21/ic_call_answer_video.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_answer_video.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-anydpi-v21/ic_call_answer_video_low.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_answer_video_low.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-anydpi-v21/ic_call_decline.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_decline.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-anydpi-v21/ic_call_decline_low.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_decline_low.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_answer.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_answer_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_answer_video.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_answer_video_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_decline.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_decline_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/notification_bg_low_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_bg_low_normal.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/notification_bg_low_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_bg_low_pressed.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/notification_bg_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_bg_normal.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_bg_normal_pressed.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/notification_oversize_large_icon_bg.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_oversize_large_icon_bg.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-hdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notify_panel_notification_icon_bg.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-ldpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_answer.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-ldpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_answer_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-ldpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_answer_video.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-ldpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_answer_video_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-ldpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_decline.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-ldpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_decline_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_answer.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_answer_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_answer_video.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_answer_video_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_decline.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_decline_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/notification_bg_low_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notification_bg_low_normal.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/notification_bg_low_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notification_bg_low_pressed.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/notification_bg_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notification_bg_normal.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notification_bg_normal_pressed.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-mdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notify_panel_notification_icon_bg.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-v21/notification_action_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_notification_action_background.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_answer.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_answer_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_answer_video.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_answer_video_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_decline.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_decline_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/notification_bg_low_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notification_bg_low_normal.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/notification_bg_low_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notification_bg_low_pressed.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/notification_bg_normal.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notification_bg_normal.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notification_bg_normal_pressed.9.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xhdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notify_panel_notification_icon_bg.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxhdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_answer.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxhdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_answer_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxhdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_answer_video.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxhdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_answer_video_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxhdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_decline.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxhdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_decline_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxxhdpi-v4/ic_call_answer.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_answer.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxxhdpi-v4/ic_call_answer_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_answer_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxxhdpi-v4/ic_call_answer_video.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_answer_video.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxxhdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_answer_video_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxxhdpi-v4/ic_call_decline.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_decline.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable-xxxhdpi-v4/ic_call_decline_low.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_decline_low.png.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable/notification_bg.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_bg.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable/notification_bg_low.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_bg_low.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable/notification_icon_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_icon_background.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/drawable/notification_tile_bg.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_tile_bg.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/layout-v21/notification_action.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21_notification_action.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/layout-v21/notification_action_tombstone.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21_notification_action_tombstone.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/layout-v21/notification_template_custom_big.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21_notification_template_custom_big.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/layout-v21/notification_template_icon_group.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21_notification_template_icon_group.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/layout/custom_dialog.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_custom_dialog.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/layout/ime_base_split_test_activity.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_ime_base_split_test_activity.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/layout/ime_secondary_split_test_activity.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_ime_secondary_split_test_activity.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/layout/notification_template_part_chronometer.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_part_chronometer.xml.flat
com.example.iptv_player.app-core-1.13.1-23\:/layout/notification_template_part_time.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_part_time.xml.flat
com.example.iptv_player.app-fragment-1.7.1-16\:/anim-v21/fragment_fast_out_extra_slow_in.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_fragment_fast_out_extra_slow_in.xml.flat
com.example.iptv_player.app-fragment-1.7.1-16\:/animator/fragment_close_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_close_enter.xml.flat
com.example.iptv_player.app-fragment-1.7.1-16\:/animator/fragment_close_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_close_exit.xml.flat
com.example.iptv_player.app-fragment-1.7.1-16\:/animator/fragment_fade_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_fade_enter.xml.flat
com.example.iptv_player.app-fragment-1.7.1-16\:/animator/fragment_fade_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_fade_exit.xml.flat
com.example.iptv_player.app-fragment-1.7.1-16\:/animator/fragment_open_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_open_enter.xml.flat
com.example.iptv_player.app-fragment-1.7.1-16\:/animator/fragment_open_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_open_exit.xml.flat
com.example.iptv_player.app-jetified-appcompat-resources-1.1.0-11\:/drawable/abc_vector_test.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_vector_test.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_edit_mode_logo.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_edit_mode_logo.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_audiotrack.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_audiotrack.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_check.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_check.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_chevron_left.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_chevron_left.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_chevron_right.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_chevron_right.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_default_album_image.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_default_album_image.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_forward.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_forward.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_fullscreen_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_fullscreen_enter.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_fullscreen_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_fullscreen_exit.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_pause_circle_filled.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_pause_circle_filled.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_play_circle_filled.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_play_circle_filled.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_rewind.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_rewind.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_settings.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_settings.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_skip_next.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_skip_next.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_skip_previous.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_skip_previous.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_speed.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_speed.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_subtitle_off.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_subtitle_off.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_ic_subtitle_on.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_subtitle_on.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_fastforward.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_fastforward.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_fullscreen_enter.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_fullscreen_enter.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_fullscreen_exit.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_fullscreen_exit.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_next.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_next.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_pause.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_pause.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_play.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_play.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_previous.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_previous.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_repeat_all.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_repeat_all.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_repeat_off.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_repeat_off.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_repeat_one.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_repeat_one.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_rewind.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_rewind.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_shuffle_off.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_shuffle_off.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_shuffle_on.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_shuffle_on.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-anydpi-v21/exo_icon_stop.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_stop.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_edit_mode_logo.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_audiotrack.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_check.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_check.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_chevron_left.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_chevron_right.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_default_album_image.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_forward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_forward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_settings.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_settings.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_skip_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_skip_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_skip_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_speed.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_speed.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_subtitle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_subtitle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_circular_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_circular_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_fastforward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_fastforward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_pause.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_pause.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_repeat_all.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_repeat_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_repeat_one.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_shuffle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_shuffle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_stop.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_stop.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-hdpi-v4/exo_icon_vr.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_vr.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_edit_mode_logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_edit_mode_logo.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_audiotrack.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_audiotrack.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_check.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_check.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_chevron_left.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_chevron_left.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_chevron_right.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_chevron_right.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_default_album_image.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_default_album_image.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_forward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_forward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_play_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_settings.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_settings.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_skip_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_skip_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_skip_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_skip_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_speed.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_speed.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_subtitle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_subtitle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_circular_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_circular_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_fastforward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_fastforward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_pause.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_pause.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_repeat_all.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_repeat_all.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_repeat_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_repeat_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_repeat_one.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_repeat_one.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_shuffle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_shuffle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_stop.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_stop.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-ldpi-v4/exo_icon_vr.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_vr.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_edit_mode_logo.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_audiotrack.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_check.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_check.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_chevron_left.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_chevron_right.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_default_album_image.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_forward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_forward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_settings.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_settings.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_skip_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_skip_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_skip_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_speed.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_speed.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_subtitle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_subtitle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_circular_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_circular_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_fastforward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_fastforward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_pause.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_pause.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_repeat_all.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_repeat_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_repeat_one.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_shuffle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_shuffle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_stop.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_stop.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-mdpi-v4/exo_icon_vr.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_vr.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_edit_mode_logo.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_audiotrack.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_check.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_check.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_chevron_left.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_chevron_right.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_default_album_image.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_forward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_forward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_settings.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_settings.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_skip_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_skip_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_skip_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_speed.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_speed.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_subtitle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_subtitle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_circular_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_circular_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_fastforward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_fastforward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_pause.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_pause.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_repeat_all.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_repeat_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_repeat_one.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_shuffle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_shuffle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_stop.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_stop.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xhdpi-v4/exo_icon_vr.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_vr.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_edit_mode_logo.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_audiotrack.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_check.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_check.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_chevron_left.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_chevron_right.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_default_album_image.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_forward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_forward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_settings.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_settings.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_skip_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_skip_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_skip_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_speed.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_speed.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_subtitle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_subtitle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_circular_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_circular_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_fastforward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_fastforward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_pause.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_pause.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_repeat_all.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_repeat_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_repeat_one.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_shuffle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_shuffle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_stop.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_stop.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxhdpi-v4/exo_icon_vr.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_vr.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_edit_mode_logo.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_audiotrack.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_check.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_check.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_chevron_left.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_chevron_right.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_default_album_image.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_forward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_forward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_settings.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_settings.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_skip_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_skip_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_skip_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_speed.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_speed.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_subtitle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_subtitle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_circular_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_circular_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_fastforward.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_fastforward.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_next.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_next.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_pause.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_pause.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_play.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_play.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_previous.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_previous.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_repeat_all.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_repeat_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_repeat_one.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_rewind.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_rewind.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_shuffle_off.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_shuffle_on.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable-xxxhdpi-v4/exo_icon_stop.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_stop.png.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/drawable/exo_rounded_rectangle.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_exo_rounded_rectangle.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/font/roboto_medium_numbers.ttf=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\font_roboto_medium_numbers.ttf.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout-v23/exo_player_control_ffwd_button.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v23_exo_player_control_ffwd_button.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout-v23/exo_player_control_rewind_button.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v23_exo_player_control_rewind_button.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_legacy_player_control_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_legacy_player_control_view.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_list_divider.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_list_divider.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_player_control_ffwd_button.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_player_control_ffwd_button.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_player_control_rewind_button.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_player_control_rewind_button.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_player_control_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_player_control_view.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_player_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_player_view.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_styled_settings_list.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_styled_settings_list.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_styled_settings_list_item.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_styled_settings_list_item.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_styled_sub_settings_list_item.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_styled_sub_settings_list_item.xml.flat
com.example.iptv_player.app-jetified-media3-ui-1.4.1-2\:/layout/exo_track_selection_dialog.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_track_selection_dialog.xml.flat
com.example.iptv_player.app-main-39\:/color/button_tint_selector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_button_tint_selector.xml.flat
com.example.iptv_player.app-main-39\:/drawable-v21/launch_background.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_launch_background.xml.flat
com.example.iptv_player.app-main-39\:/drawable/button_selector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_selector.xml.flat
com.example.iptv_player.app-main-39\:/drawable/gradient_bottom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_gradient_bottom.xml.flat
com.example.iptv_player.app-main-39\:/drawable/gradient_top.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_gradient_top.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_arrow_back.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_arrow_back.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_audiotrack.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_audiotrack.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_forward_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_forward_10.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_fullscreen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_fullscreen.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_hd.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_hd.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_replay_10.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_replay_10.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_settings.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_settings.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_skip_next.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_skip_next.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_skip_previous.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_skip_previous.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_subtitles.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_subtitles.xml.flat
com.example.iptv_player.app-main-39\:/drawable/ic_volume_up.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_volume_up.xml.flat
com.example.iptv_player.app-main-39\:/drawable/launch_logo.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_launch_logo.xml.flat
com.example.iptv_player.app-main-39\:/drawable/launch_logo_vector.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_launch_logo_vector.xml.flat
com.example.iptv_player.app-main-39\:/drawable/loading_dots.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_loading_dots.xml.flat
com.example.iptv_player.app-main-39\:/drawable/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logo.png.flat
com.example.iptv_player.app-main-39\:/drawable/logo_small.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logo_small.xml.flat
com.example.iptv_player.app-main-39\:/drawable/logo_splash.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logo_splash.xml.flat
com.example.iptv_player.app-main-39\:/drawable/splash_screen.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_splash_screen.xml.flat
com.example.iptv_player.app-main-39\:/layout/activity_exoplayer.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_activity_exoplayer.xml.flat
com.example.iptv_player.app-main-39\:/layout/custom_player_control_view.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_custom_player_control_view.xml.flat
com.example.iptv_player.app-main-39\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-39\:/mipmap-hdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_logo.png.flat
com.example.iptv_player.app-main-39\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-39\:/mipmap-mdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_logo.png.flat
com.example.iptv_player.app-main-39\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-39\:/mipmap-xhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_logo.png.flat
com.example.iptv_player.app-main-39\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-39\:/mipmap-xxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_logo.png.flat
com.example.iptv_player.app-main-39\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.example.iptv_player.app-main-39\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_logo.png.flat
com.example.iptv_player.app-main-39\:/xml/network_security_config.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_network_security_config.xml.flat
com.example.iptv_player.app-media-1.7.0-8\:/layout/notification_media_action.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_media_action.xml.flat
com.example.iptv_player.app-media-1.7.0-8\:/layout/notification_media_cancel_action.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_media_cancel_action.xml.flat
com.example.iptv_player.app-media-1.7.0-8\:/layout/notification_template_big_media.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_big_media.xml.flat
com.example.iptv_player.app-media-1.7.0-8\:/layout/notification_template_big_media_custom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_big_media_custom.xml.flat
com.example.iptv_player.app-media-1.7.0-8\:/layout/notification_template_big_media_narrow.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_big_media_narrow.xml.flat
com.example.iptv_player.app-media-1.7.0-8\:/layout/notification_template_big_media_narrow_custom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_big_media_narrow_custom.xml.flat
com.example.iptv_player.app-media-1.7.0-8\:/layout/notification_template_lines_media.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_lines_media.xml.flat
com.example.iptv_player.app-media-1.7.0-8\:/layout/notification_template_media.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_media.xml.flat
com.example.iptv_player.app-media-1.7.0-8\:/layout/notification_template_media_custom.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_media_custom.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/drawable-v21/ic_arrow_down_24dp.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_ic_arrow_down_24dp.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/drawable-v21/preference_list_divider_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_preference_list_divider_material.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/expand_button.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_expand_button.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/image_frame.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_image_frame.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_category.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_category.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_category_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_category_material.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_dialog_edittext.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_dialog_edittext.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_dropdown.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_dropdown.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_dropdown_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_dropdown_material.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_information.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_information.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_information_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_information_material.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_list_fragment.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_list_fragment.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_material.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_recyclerview.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_recyclerview.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_widget_checkbox.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_widget_checkbox.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_widget_seekbar.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_widget_seekbar.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_widget_seekbar_material.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_widget_seekbar_material.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_widget_switch.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_widget_switch.xml.flat
com.example.iptv_player.app-preference-1.2.1-0\:/layout/preference_widget_switch_compat.xml=C\:\\Users\\alkhsilat\\Documents\\augment-projects\\FlutterNetflixAndroidtv\\iptv_player\\build\\app\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_preference_widget_switch_compat.xml.flat
