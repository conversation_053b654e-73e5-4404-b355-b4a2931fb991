import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../models/xtreme_api_models.dart';
import '../services/xtreme_api_service.dart';

class SmartVideoPlayerScreen extends StatefulWidget {
  final VodContent movie;

  const SmartVideoPlayerScreen({
    Key? key,
    required this.movie,
  }) : super(key: key);

  @override
  State<SmartVideoPlayerScreen> createState() => _SmartVideoPlayerScreenState();
}

class _SmartVideoPlayerScreenState extends State<SmartVideoPlayerScreen> {
  final XtremeApiService _apiService = XtremeApiService();
  
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  bool _isLoading = true;
  bool _hasError = false;
  bool _isReencoding = false;
  String? _errorMessage;
  String? _currentStreamUrl;
  int _retryAttempt = 0;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _keepScreenOn();
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoPlayerController?.dispose();
    WakelockPlus.disable();
    super.dispose();
  }

  Future<void> _keepScreenOn() async {
    await WakelockPlus.enable();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _isReencoding = false;
      });

      // Get the original video stream URL
      final originalStreamUrl = _apiService.getVodStreamUrl(
        widget.movie.streamId,
        widget.movie.containerExtension,
      );

      print('🎬 Initializing Smart Player for: ${widget.movie.name}');
      print('📺 Original Stream URL: $originalStreamUrl');

      // Try original stream first
      await _tryPlayStream(originalStreamUrl, isOriginal: true);

    } catch (e) {
      print('❌ Error in Smart Player initialization: $e');
      await _handlePlaybackError(e.toString());
    }
  }

  Future<void> _tryPlayStream(String streamUrl, {bool isOriginal = false}) async {
    try {
      _currentStreamUrl = streamUrl;
      
      // Initialize video player controller with optimized settings
      _videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(streamUrl),
        videoPlayerOptions: VideoPlayerOptions(
          allowBackgroundPlayback: true,
          mixWithOthers: false,
        ),
        httpHeaders: {
          'User-Agent': 'ExoPlayerLib/2.18.1 (Linux; Android 10; Android TV) ExoPlayerLib/2.18.1',
          'Accept': '*/*',
          'Accept-Encoding': 'identity',
          'Connection': 'keep-alive',
          'Cache-Control': 'no-cache',
        },
      );

      await _videoPlayerController!.initialize();

      // Initialize Chewie controller
      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: true,
        looping: false,
        allowFullScreen: false,
        allowMuting: true,
        allowPlaybackSpeedChanging: false,
        showControlsOnInitialize: true,
        hideControlsTimer: const Duration(seconds: 4),
        materialProgressColors: ChewieProgressColors(
          playedColor: const Color(0xFFE17055),
          handleColor: const Color(0xFFE17055),
          bufferedColor: Colors.white38,
          backgroundColor: Colors.white24,
        ),
        errorBuilder: (context, errorMessage) {
          return _buildErrorWidget(errorMessage);
        },
      );

      setState(() {
        _isLoading = false;
        _isReencoding = false;
      });

      print('✅ Smart Player initialized successfully with ${isOriginal ? 'original' : 're-encoded'} stream');
    } catch (e) {
      print('❌ Error playing stream: $e');
      throw e;
    }
  }

  Future<void> _handlePlaybackError(String error) async {
    print('🔄 Handling playback error, attempt: $_retryAttempt');
    
    if (_retryAttempt == 0 && error.contains('MediaCodecVideoRenderer')) {
      // First attempt failed due to codec issue, try re-encoding
      _retryAttempt++;
      await _tryReencodeAndPlay();
    } else if (_retryAttempt == 1) {
      // Re-encoding also failed, try alternative stream formats
      _retryAttempt++;
      await _tryAlternativeFormats();
    } else {
      // All attempts failed, show error
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = _getErrorMessage(error);
      });
    }
  }

  Future<void> _tryReencodeAndPlay() async {
    try {
      setState(() {
        _isReencoding = true;
        _isLoading = true;
      });

      print('🔄 Attempting to re-encode video for compatibility...');

      // Get original stream URL
      final originalStreamUrl = _apiService.getVodStreamUrl(
        widget.movie.streamId,
        widget.movie.containerExtension,
      );

      // Create a compatible re-encoded stream URL
      final reEncodedUrl = await _createReEncodedStream(originalStreamUrl);
      
      if (reEncodedUrl != null) {
        await _tryPlayStream(reEncodedUrl, isOriginal: false);
      } else {
        throw Exception('Re-encoding failed');
      }
    } catch (e) {
      print('❌ Re-encoding failed: $e');
      await _handlePlaybackError(e.toString());
    }
  }

  Future<String?> _createReEncodedStream(String originalUrl) async {
    try {
      // For now, we'll try alternative stream parameters
      // In a real implementation, you might:
      // 1. Use FFmpeg to re-encode locally (heavy operation)
      // 2. Request a different format from your IPTV provider
      // 3. Use a proxy service for re-encoding

      // Try requesting HLS format instead of MP4
      if (originalUrl.contains('.mp4')) {
        final hlsUrl = originalUrl.replaceAll('.mp4', '.m3u8');
        print('🔄 Trying HLS format: $hlsUrl');
        return hlsUrl;
      }

      // Try different container format
      if (originalUrl.contains('.mp4')) {
        final tsUrl = originalUrl.replaceAll('.mp4', '.ts');
        print('🔄 Trying TS format: $tsUrl');
        return tsUrl;
      }

      return null;
    } catch (e) {
      print('❌ Error creating re-encoded stream: $e');
      return null;
    }
  }

  Future<void> _tryAlternativeFormats() async {
    try {
      print('🔄 Trying alternative stream formats...');
      
      // Try with different user agent (some servers provide different formats)
      final originalStreamUrl = _apiService.getVodStreamUrl(
        widget.movie.streamId,
        widget.movie.containerExtension,
      );

      // Try with mobile user agent (might get lower resolution)
      _videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(originalStreamUrl),
        httpHeaders: {
          'User-Agent': 'Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36',
          'Accept': 'video/mp4,video/*;q=0.9,*/*;q=0.8',
        },
      );

      await _videoPlayerController!.initialize();
      
      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: true,
        looping: false,
        allowFullScreen: false,
        materialProgressColors: ChewieProgressColors(
          playedColor: const Color(0xFFE17055),
          handleColor: const Color(0xFFE17055),
          bufferedColor: Colors.white38,
          backgroundColor: Colors.white24,
        ),
      );

      setState(() {
        _isLoading = false;
      });

      print('✅ Alternative format working');
    } catch (e) {
      print('❌ Alternative formats also failed: $e');
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'This video format is not compatible with your device. Please try a different movie.';
      });
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('MediaCodecVideoRenderer')) {
      return 'Video format not supported on this device. We tried multiple compatibility options but this specific movie uses a codec that is not compatible with your Android TV.';
    } else if (error.contains('Source error')) {
      return 'Unable to load video stream. Please check your internet connection and try again.';
    } else {
      return 'Unable to play video. Please try a different movie or check your connection.';
    }
  }

  Widget _buildErrorWidget(String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 64,
          ),
          const SizedBox(height: 20),
          const Text(
            'Video Error',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Text(
              errorMessage,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  void _goBack() {
    Navigator.of(context).pop();
  }

  void _retry() {
    _retryAttempt = 0;
    _initializePlayer();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.goBack ||
                event.logicalKey == LogicalKeyboardKey.escape) {
              _goBack();
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: Stack(
          children: [
            _buildVideoPlayer(),
            if (_hasError) _buildBackButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            ),
            const SizedBox(height: 20),
            Text(
              _isReencoding 
                  ? 'Optimizing video for your device...' 
                  : 'Loading video...',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            if (_isReencoding) ...[
              const SizedBox(height: 10),
              const Text(
                'This may take a moment',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ],
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 20),
            const Text(
              'Playback Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                _errorMessage ?? 'Unknown error',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _retry,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFE17055),
                  ),
                  child: const Text(
                    'Try Again',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 20),
                ElevatedButton(
                  onPressed: _goBack,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                  ),
                  child: const Text(
                    'Go Back',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    if (_chewieController != null) {
      return Center(
        child: Chewie(
          controller: _chewieController!,
        ),
      );
    }

    return const Center(
      child: Text(
        'Video not available',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return Positioned(
      top: 40,
      left: 40,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(25),
        ),
        child: IconButton(
          onPressed: _goBack,
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }
}
