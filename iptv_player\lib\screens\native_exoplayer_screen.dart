import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/xtreme_api_models.dart';
import '../services/xtreme_api_service.dart';

class NativeExoPlayerScreen extends StatefulWidget {
  final VodContent movie;

  const NativeExoPlayerScreen({
    Key? key,
    required this.movie,
  }) : super(key: key);

  @override
  State<NativeExoPlayerScreen> createState() => _NativeExoPlayerScreenState();
}

class _NativeExoPlayerScreenState extends State<NativeExoPlayerScreen> {
  final XtremeApiService _apiService = XtremeApiService();
  static const platform = MethodChannel('com.example.iptv_player/exoplayer');
  
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;
  String? _streamUrl;

  @override
  void initState() {
    super.initState();
    _launchNativeExoPlayer();
  }

  Future<void> _launchNativeExoPlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Get the video stream URL
      _streamUrl = _apiService.getVodStreamUrl(
        widget.movie.streamId,
        widget.movie.containerExtension,
      );

      print('🎬 Launching Native ExoPlayer for: ${widget.movie.name}');
      print('📺 Stream URL: $_streamUrl');

      // Launch native ExoPlayer activity
      final bool result = await platform.invokeMethod('launchExoPlayer', {
        'streamUrl': _streamUrl,
        'movieTitle': widget.movie.name,
        'movieId': widget.movie.streamId.toString(),
        'movieThumbnail': widget.movie.streamIcon,
      });

      if (result) {
        print('✅ Native ExoPlayer launched successfully');
        _onExoPlayerLaunched();
      } else {
        throw Exception('Failed to launch ExoPlayer');
      }

    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = _getErrorMessage(e.toString());
      });
      print('❌ Error launching Native ExoPlayer: $e');
    }
  }

  void _onExoPlayerLaunched() {
    // Go back immediately - no loading screen needed
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('ExoPlayer')) {
      return 'Native ExoPlayer initialization failed. This may be due to device compatibility issues.';
    } else if (error.contains('network') || error.contains('connection')) {
      return 'Unable to connect to video stream. Please check your internet connection.';
    } else {
      return 'Unable to launch native video player. Please try again.';
    }
  }

  void _goBack() {
    Navigator.of(context).pop();
  }

  void _retry() {
    _launchNativeExoPlayer();
  }

  void _copyStreamUrl() {
    if (_streamUrl != null) {
      Clipboard.setData(ClipboardData(text: _streamUrl!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Stream URL copied to clipboard'),
          backgroundColor: Color(0xFFE17055),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.goBack ||
                event.logicalKey == LogicalKeyboardKey.escape) {
              _goBack();
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: Center(
          child: _buildContent(),
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
          ),
          const SizedBox(height: 30),
          Text(
            'Launching Native ExoPlayer...',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 15),
          Text(
            widget.movie.name,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          Container(
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.symmetric(horizontal: 40),
            decoration: BoxDecoration(
              color: const Color(0xFFE17055).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFFE17055).withOpacity(0.3)),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.video_library,
                  color: const Color(0xFFE17055),
                  size: 32,
                ),
                const SizedBox(height: 10),
                const Text(
                  'Native ExoPlayer provides superior codec support and hardware acceleration for the best video playback experience.',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      );
    }

    if (_hasError) {
      return Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 80,
            ),
            const SizedBox(height: 30),
            const Text(
              'ExoPlayer Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Text(
              _errorMessage ?? 'Unknown error',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            
            // Retry button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _retry,
                icon: const Icon(Icons.refresh, color: Colors.white),
                label: const Text(
                  'Try Again',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE17055),
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 15),
            
            // Copy URL button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _copyStreamUrl,
                icon: const Icon(Icons.copy, color: Colors.white70),
                label: const Text(
                  'Copy Stream URL',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 16,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: Colors.white30),
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 15),
            
            // Go back button
            SizedBox(
              width: double.infinity,
              child: TextButton.icon(
                onPressed: _goBack,
                icon: const Icon(Icons.arrow_back, color: Colors.white54),
                label: const Text(
                  'Go Back',
                  style: TextStyle(
                    color: Colors.white54,
                    fontSize: 16,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Success state (ExoPlayer launched)
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 80,
        ),
        const SizedBox(height: 30),
        const Text(
          'Native ExoPlayer Launched!',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 15),
        Text(
          'Playing: ${widget.movie.name}',
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 30),
        ElevatedButton(
          onPressed: _goBack,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFE17055),
            padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
          ),
          child: const Text(
            'Back to Movies',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }
}
