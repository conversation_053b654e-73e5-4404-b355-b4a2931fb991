<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/transparent">

    <!-- Hide all default ExoPlayer controls -->
    <View
        android:id="@+id/exo_prev"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/exo_rew_with_amount"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/exo_ffwd_with_amount"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/exo_next"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <!-- Top controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/gradient_top"
        android:padding="16dp"
        android:gravity="center_vertical">

        <ImageButton
            android:id="@+id/exo_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/button_selector"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="Back"
            android:tint="@color/button_tint_selector"
            android:focusable="true"
            android:clickable="true" />

        <TextView
            android:id="@+id/exo_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text=""
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

    </LinearLayout>

    <!-- Spacer to push controls to bottom -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Center controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@android:color/transparent"
        android:padding="20dp">

        <ImageButton
            android:id="@+id/exo_rew"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="24dp"
            android:background="@drawable/button_selector"
            android:src="@drawable/ic_replay_10"
            android:contentDescription="Rewind 10 seconds"
            android:tint="@color/button_tint_selector"
            android:focusable="true"
            android:clickable="true" />

        <ImageButton
            android:id="@+id/exo_play_pause"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:background="@drawable/button_selector"
            android:contentDescription="Play/Pause"
            android:tint="@color/button_tint_selector"
            android:focusable="true"
            android:clickable="true" />

        <ImageButton
            android:id="@+id/exo_ffwd"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginStart="24dp"
            android:background="@drawable/button_selector"
            android:src="@drawable/ic_forward_10"
            android:contentDescription="Forward 10 seconds"
            android:tint="@color/button_tint_selector"
            android:focusable="true"
            android:clickable="true" />

    </LinearLayout>

    <!-- Timeline at absolute bottom -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/gradient_bottom"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingTop="16dp"
        android:paddingBottom="8dp">

        <!-- Progress bar -->
        <androidx.media3.ui.DefaultTimeBar
            android:id="@+id/exo_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp" />

        <!-- Time indicators and settings -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/exo_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:text="00:00" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:text="/" />

            <TextView
                android:id="@+id/exo_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:text="00:00" />

            <!-- Spacer -->
            <View
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <!-- Settings button in bottom bar -->
            <ImageButton
                android:id="@+id/exo_settings"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/button_selector"
                android:src="@drawable/ic_settings"
                android:contentDescription="Settings"
                android:tint="@color/button_tint_selector"
                android:focusable="true"
                android:clickable="true" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
