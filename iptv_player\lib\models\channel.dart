class Channel {
  final String id;
  final String name;
  final String streamUrl;
  final String? logoUrl;
  final String? description;
  final String category;
  final bool isLive;
  final String? epgId;
  final Map<String, dynamic>? metadata;

  Channel({
    required this.id,
    required this.name,
    required this.streamUrl,
    this.logoUrl,
    this.description,
    required this.category,
    this.isLive = true,
    this.epgId,
    this.metadata,
  });

  factory Channel.fromJson(Map<String, dynamic> json) {
    return Channel(
      id: json['stream_id']?.toString() ?? json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      streamUrl: json['stream_url'] ?? '',
      logoUrl: json['stream_icon'] ?? json['logo_url'],
      description: json['description'],
      category: json['category_name'] ?? json['category'] ?? 'General',
      isLive: json['is_live'] ?? true,
      epgId: json['epg_channel_id']?.toString(),
      metadata: json,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'stream_url': streamUrl,
      'logo_url': logoUrl,
      'description': description,
      'category': category,
      'is_live': isLive,
      'epg_channel_id': epgId,
      'metadata': metadata,
    };
  }
}

class ChannelCategory {
  final String id;
  final String name;
  final List<Channel> channels;
  final String? iconUrl;

  ChannelCategory({
    required this.id,
    required this.name,
    required this.channels,
    this.iconUrl,
  });

  factory ChannelCategory.fromJson(Map<String, dynamic> json, List<Channel> channels) {
    return ChannelCategory(
      id: json['category_id']?.toString() ?? json['id']?.toString() ?? '',
      name: json['category_name'] ?? json['name'] ?? '',
      channels: channels,
      iconUrl: json['icon_url'],
    );
  }
}

class Movie {
  final String id;
  final String title;
  final String streamUrl;
  final String? posterUrl;
  final String? backdropUrl;
  final String? description;
  final String? genre;
  final String? year;
  final String? rating;
  final String? duration;
  final Map<String, dynamic>? metadata;

  Movie({
    required this.id,
    required this.title,
    required this.streamUrl,
    this.posterUrl,
    this.backdropUrl,
    this.description,
    this.genre,
    this.year,
    this.rating,
    this.duration,
    this.metadata,
  });

  factory Movie.fromJson(Map<String, dynamic> json) {
    return Movie(
      id: json['stream_id']?.toString() ?? json['id']?.toString() ?? '',
      title: json['name'] ?? json['title'] ?? '',
      streamUrl: json['stream_url'] ?? '',
      posterUrl: json['stream_icon'] ?? json['poster_url'],
      backdropUrl: json['backdrop_url'],
      description: json['plot'] ?? json['description'],
      genre: json['genre'],
      year: json['releasedate'] ?? json['year'],
      rating: json['rating'],
      duration: json['duration'],
      metadata: json,
    );
  }
}

class Series {
  final String id;
  final String title;
  final String? posterUrl;
  final String? backdropUrl;
  final String? description;
  final String? genre;
  final String? year;
  final String? rating;
  final List<Season> seasons;
  final Map<String, dynamic>? metadata;

  Series({
    required this.id,
    required this.title,
    this.posterUrl,
    this.backdropUrl,
    this.description,
    this.genre,
    this.year,
    this.rating,
    required this.seasons,
    this.metadata,
  });

  factory Series.fromJson(Map<String, dynamic> json, List<Season> seasons) {
    return Series(
      id: json['series_id']?.toString() ?? json['id']?.toString() ?? '',
      title: json['name'] ?? json['title'] ?? '',
      posterUrl: json['cover'] ?? json['poster_url'],
      backdropUrl: json['backdrop_url'],
      description: json['plot'] ?? json['description'],
      genre: json['genre'],
      year: json['releaseDate'] ?? json['year'],
      rating: json['rating'],
      seasons: seasons,
      metadata: json,
    );
  }
}

class Season {
  final String id;
  final String name;
  final int seasonNumber;
  final List<Episode> episodes;

  Season({
    required this.id,
    required this.name,
    required this.seasonNumber,
    required this.episodes,
  });

  factory Season.fromJson(Map<String, dynamic> json, List<Episode> episodes) {
    return Season(
      id: json['season_id']?.toString() ?? json['id']?.toString() ?? '',
      name: json['name'] ?? 'Season ${json['season_number'] ?? ''}',
      seasonNumber: json['season_number'] ?? 1,
      episodes: episodes,
    );
  }
}

class Episode {
  final String id;
  final String title;
  final String streamUrl;
  final String? description;
  final int episodeNumber;
  final String? duration;
  final String? thumbnailUrl;

  Episode({
    required this.id,
    required this.title,
    required this.streamUrl,
    this.description,
    required this.episodeNumber,
    this.duration,
    this.thumbnailUrl,
  });

  factory Episode.fromJson(Map<String, dynamic> json) {
    return Episode(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? json['episode_name'] ?? '',
      streamUrl: json['stream_url'] ?? '',
      description: json['plot'] ?? json['description'],
      episodeNumber: json['episode_num'] ?? 1,
      duration: json['duration'],
      thumbnailUrl: json['info']?['movie_image'],
    );
  }
}
