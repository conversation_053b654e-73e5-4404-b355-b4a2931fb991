import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:better_player/better_player.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../models/xtreme_api_models.dart';
import '../services/xtreme_api_service.dart';

class BetterVideoPlayerScreen extends StatefulWidget {
  final VodContent movie;

  const BetterVideoPlayerScreen({
    Key? key,
    required this.movie,
  }) : super(key: key);

  @override
  State<BetterVideoPlayerScreen> createState() => _BetterVideoPlayerScreenState();
}

class _BetterVideoPlayerScreenState extends State<BetterVideoPlayerScreen> {
  final XtremeApiService _apiService = XtremeApiService();
  
  BetterPlayerController? _betterPlayerController;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _keepScreenOn();
  }

  @override
  void dispose() {
    _betterPlayerController?.dispose();
    WakelockPlus.disable();
    super.dispose();
  }

  Future<void> _keepScreenOn() async {
    await WakelockPlus.enable();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Get the video stream URL
      final streamUrl = _apiService.getVodStreamUrl(
        widget.movie.streamId,
        widget.movie.containerExtension,
      );

      print('🎬 Initializing Better Player for: ${widget.movie.name}');
      print('📺 Stream URL: $streamUrl');

      // Configure Better Player for Android TV IPTV
      BetterPlayerConfiguration betterPlayerConfiguration = BetterPlayerConfiguration(
        aspectRatio: 16 / 9,
        autoPlay: true,
        looping: false,
        fullScreenByDefault: true,
        allowedScreenSleep: false,
        fit: BoxFit.contain,
        handleLifecycle: true,
        autoDetectFullscreenDeviceOrientation: false,
        autoDetectFullscreenAspectRatio: false,
        startAt: Duration.zero,
        controlsConfiguration: BetterPlayerControlsConfiguration(
          enableFullscreen: false, // We're already fullscreen
          enableMute: true,
          enablePlayPause: true,
          enableProgressBar: true,
          enableProgressText: true,
          enableSkips: true,
          enableSubtitles: false,
          enablePlaybackSpeed: false,
          enablePip: false,
          enableRetry: true,
          skipBackIcon: Icons.replay_10,
          skipForwardIcon: Icons.forward_10,
          playIcon: Icons.play_arrow,
          pauseIcon: Icons.pause,
          muteIcon: Icons.volume_off,
          unMuteIcon: Icons.volume_up,
          progressBarPlayedColor: const Color(0xFFE17055),
          progressBarHandleColor: const Color(0xFFE17055),
          progressBarBufferedColor: Colors.white38,
          progressBarBackgroundColor: Colors.white24,
          textColor: Colors.white,
          iconsColor: Colors.white,
          controlBarColor: Colors.black54,
          controlBarHeight: 80,
          loadingColor: const Color(0xFFE17055),
          showControlsOnInitialize: true,
          controlsHideTime: const Duration(seconds: 4),
          playerTheme: BetterPlayerTheme.material,
        ),
        eventListener: (BetterPlayerEvent event) {
          print('🎬 Player Event: ${event.betterPlayerEventType}');
          if (event.betterPlayerEventType == BetterPlayerEventType.exception) {
            print('❌ Player Exception: ${event.parameters}');
          }
        },
      );

      // Configure data source for IPTV streaming
      BetterPlayerDataSource dataSource = BetterPlayerDataSource(
        BetterPlayerDataSourceType.network,
        streamUrl,
        videoFormat: BetterPlayerVideoFormat.other,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Linux; Android 10; Android TV) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': '*/*',
          'Accept-Encoding': 'identity',
          'Connection': 'keep-alive',
        },
        useAsmsSubtitles: false,
        useAsmsAudioTracks: false,
        bufferingConfiguration: BetterPlayerBufferingConfiguration(
          minBufferMs: 3000,      // Increased for better stability
          maxBufferMs: 15000,     // Increased for smoother playback
          bufferForPlaybackMs: 1500,
          bufferForPlaybackAfterRebufferMs: 3000,
        ),
        cacheConfiguration: BetterPlayerCacheConfiguration(
          useCache: true,
          preCacheSize: 10 * 1024 * 1024, // 10MB pre-cache
          maxCacheSize: 100 * 1024 * 1024, // 100MB max cache
          maxCacheFileSize: 50 * 1024 * 1024, // 50MB max file cache
        ),
      );

      _betterPlayerController = BetterPlayerController(
        betterPlayerConfiguration,
        betterPlayerDataSource: dataSource,
      );

      // Wait for initialization
      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        _isLoading = false;
      });

      print('✅ Better Player initialized successfully');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = _getErrorMessage(e.toString());
      });
      print('❌ Error initializing Better Player: $e');
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('MediaCodecVideoRenderer')) {
      return 'Video format not supported on this device. The movie may use a codec that is not compatible with your Android TV.';
    } else if (error.contains('Source error')) {
      return 'Unable to load video stream. Please check your internet connection and try again.';
    } else if (error.contains('Cleartext')) {
      return 'Network security error. Please try again.';
    } else {
      return 'Unable to play video. Please try a different movie or check your connection.';
    }
  }

  void _goBack() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            // Handle back button
            if (event.logicalKey == LogicalKeyboardKey.goBack ||
                event.logicalKey == LogicalKeyboardKey.escape) {
              _goBack();
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: Stack(
          children: [
            // Video player
            _buildVideoPlayer(),
            
            // Back button overlay
            _buildBackButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            ),
            SizedBox(height: 20),
            Text(
              'Loading video...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 20),
            const Text(
              'Failed to load video',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                _errorMessage ?? 'Unknown error',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _initializePlayer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFE17055),
                  ),
                  child: const Text(
                    'Retry',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 20),
                ElevatedButton(
                  onPressed: _goBack,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                  ),
                  child: const Text(
                    'Go Back',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    if (_betterPlayerController != null) {
      return BetterPlayer(
        controller: _betterPlayerController!,
      );
    }

    return const Center(
      child: Text(
        'Video not available',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return Positioned(
      top: 40,
      left: 40,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.5),
          borderRadius: BorderRadius.circular(25),
        ),
        child: IconButton(
          onPressed: _goBack,
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }
}
