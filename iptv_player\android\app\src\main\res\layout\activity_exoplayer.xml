<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    android:keepScreenOn="true">

    <androidx.media3.ui.PlayerView
        android:id="@+id/player_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black"
        app:use_controller="true"
        app:auto_show="true"
        app:hide_on_touch="true"
        app:show_buffering="when_playing"
        app:controller_layout_id="@layout/custom_player_control_view"
        app:resize_mode="fit" />

</FrameLayout>
