import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/directional_focus_widget.dart';
import '../models/user_profile.dart';
import '../services/local_database_service.dart';
import '../services/xtreme_api_service.dart';
import 'profile_selection_screen.dart';
import 'movies_screen.dart';
import 'settings_screen.dart';
import 'search_screen.dart';

class GoPlayHomeScreen extends StatefulWidget {
  const GoPlayHomeScreen({Key? key}) : super(key: key);

  @override
  State<GoPlayHomeScreen> createState() => _GoPlayHomeScreenState();
}

class _GoPlayHomeScreenState extends State<GoPlayHomeScreen> {
  int _selectedCategoryIndex = 0;
  UserProfile? _currentProfile;
  final LocalDatabaseService _dbService = LocalDatabaseService();
  final XtremeApiService _apiService = XtremeApiService();

  // Category counts - start with 0 and loading state
  int _continueWatchingCount = 0;
  int _liveChannelsCount = 0;
  int _moviesCount = 0;
  int _seriesCount = 0;
  int _trendingCount = 0;
  int _myListCount = 0;
  bool _isLoadingCounts = true;
  
  @override
  void initState() {
    super.initState();
    _loadCurrentProfile();
    _loadCategoryCounts();
  }

  Future<void> _loadCurrentProfile() async {
    try {
      final profile = await _dbService.getCurrentProfile();
      setState(() {
        _currentProfile = profile;
        _continueWatchingCount = profile?.continueWatching.length ?? 0;
      });
    } catch (e) {
      print('Error loading current profile: $e');
    }
  }

  Future<void> _loadCategoryCounts() async {
    // Start with loading = true to show "Loading..." state
    setState(() {
      _isLoadingCounts = true;
    });

    // Load favorites count immediately (it's fast)
    _loadFavoritesCount();

    // Load API counts in background with timeout
    _loadApiCountsInBackground();
  }

  Future<void> _loadFavoritesCount() async {
    try {
      final favCount = await _getFavoritesCount();
      setState(() {
        _myListCount = favCount;
      });
    } catch (e) {
      print('❌ Error loading favorites count: $e');
    }
  }

  Future<void> _loadApiCountsInBackground() async {
    try {
      print('🔍 Checking API authentication status...');
      print('🔍 Is authenticated: ${_apiService.isAuthenticated}');

      // Check if API service is authenticated
      if (!_apiService.isAuthenticated) {
        print('⚠️ API service not authenticated, trying to re-authenticate...');

        // Try to get saved credentials and re-authenticate
        final credentials = await _dbService.getSavedLoginCredentials();
        if (credentials != null) {
          print('🔄 Found saved credentials, re-authenticating...');
          final success = await _apiService.authenticate(
            credentials['username']!,
            credentials['password']!,
          );

          if (!success) {
            print('❌ Re-authentication failed, stopping loading');
            setState(() {
              _isLoadingCounts = false;
            });
            return;
          }
          print('✅ Re-authentication successful!');
        } else {
          print('❌ No saved credentials found, stopping loading');
          setState(() {
            _isLoadingCounts = false;
          });
          return;
        }
      }

      print('🚀 API authenticated, loading real counts...');
      // Load each category separately with timeout
      _loadLiveChannelsCount();
      _loadMoviesCount();
      _loadSeriesCount();

    } catch (e) {
      print('❌ Error loading API counts: $e');
      // Stop loading on error
      setState(() {
        _isLoadingCounts = false;
      });
    }
  }

  Future<void> _loadLiveChannelsCount() async {
    try {
      final channels = await _apiService.getLiveChannels().timeout(
        const Duration(seconds: 10),
        onTimeout: () => [],
      );
      setState(() {
        _liveChannelsCount = channels.length;
        _isLoadingCounts = false; // Stop loading when first category loads
      });
      print('� Live channels loaded: ${channels.length}');
    } catch (e) {
      print('❌ Error loading live channels: $e');
    }
  }

  Future<void> _loadMoviesCount() async {
    try {
      final movies = await _apiService.getVodContent().timeout(
        const Duration(seconds: 10),
        onTimeout: () => [],
      );
      setState(() {
        _moviesCount = movies.length;
        _trendingCount = (movies.length * 0.1).round(); // 10% as trending
      });
      print('🎬 Movies loaded: ${movies.length}');
    } catch (e) {
      print('❌ Error loading movies: $e');
    }
  }

  Future<void> _loadSeriesCount() async {
    try {
      final series = await _apiService.getSeriesContent().timeout(
        const Duration(seconds: 10),
        onTimeout: () => [],
      );
      setState(() {
        _seriesCount = series.length;
      });
      print('📺 Series loaded: ${series.length}');
    } catch (e) {
      print('❌ Error loading series: $e');
    }
  }

  Future<int> _getFavoritesCount() async {
    try {
      if (_currentProfile == null) return 0;

      final totalFavorites = _currentProfile!.favoriteChannels.length +
                           _currentProfile!.favoriteMovies.length +
                           _currentProfile!.favoriteSeries.length;
      return totalFavorites;
    } catch (e) {
      print('❌ Error getting favorites count: $e');
      return 0;
    }
  }

  List<CategoryItem> get _categories => [
    CategoryItem(
      title: 'Live Channel',
      subtitle: _isLoadingCounts
          ? 'Loading...'
          : _liveChannelsCount > 0
              ? '$_liveChannelsCount channels'
              : 'No channels',
      icon: Icons.live_tv,
      color: const Color(0xFF6C5CE7),
    ),
    CategoryItem(
      title: 'Movies',
      subtitle: _isLoadingCounts
          ? 'Loading...'
          : _moviesCount > 0
              ? '$_moviesCount movies'
              : 'No movies',
      icon: Icons.movie,
      color: const Color(0xFFE17055),
    ),
    CategoryItem(
      title: 'Series',
      subtitle: _isLoadingCounts
          ? 'Loading...'
          : _seriesCount > 0
              ? '$_seriesCount series'
              : 'No series',
      icon: Icons.tv,
      color: const Color(0xFF00B894),
    ),
    CategoryItem(
      title: 'Trend',
      subtitle: _isLoadingCounts
          ? 'Loading...'
          : _trendingCount > 0
              ? '$_trendingCount trending'
              : 'No trending',
      icon: Icons.trending_up,
      color: const Color(0xFFFDCB6E),
    ),
    CategoryItem(
      title: 'mylist',
      subtitle: _isLoadingCounts
          ? 'Loading...'
          : _myListCount > 0
              ? '$_myListCount favorites'
              : 'No favorites',
      icon: Icons.favorite,
      color: const Color(0xFF74B9FF),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: FocusTraversalGroup(
        policy: ReadingOrderTraversalPolicy(),
        child: SizedBox(
          height: MediaQuery.of(context).size.height, // 100vh
          width: MediaQuery.of(context).size.width,   // 100vw
          child: Padding(
            padding: const EdgeInsets.fromLTRB(40, 20, 40, 30),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(),

                const SizedBox(height: 30),

                // Main Categories Grid
                Expanded(
                  flex: 3,
                  child: _buildCategoriesGrid(),
                ),

                const SizedBox(height: 20),

                // Continue Watching Section
                Expanded(
                  flex: 2,
                  child: _buildContinueWatching(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Logo
        Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  'assets/images/logo.png',
                  width: 60,
                  height: 60,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback to gradient if logo not found
                    return Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFFB347), Color(0xFFFF6B35)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                        child: Text(
                          'GO',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 16),
            const Text(
              'GOPLAY TV',
              style: TextStyle(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        
        // Action Icons (Profile, Settings, Search - right to left)
        FocusTraversalGroup(
          policy: OrderedTraversalPolicy(),
          child: Row(
            children: [
              FocusTraversalOrder(
                order: const NumericFocusOrder(1),
                child: _buildActionIcon(Icons.search, 2, "Search"),
              ),
              const SizedBox(width: 16),
              FocusTraversalOrder(
                order: const NumericFocusOrder(2),
                child: _buildActionIcon(Icons.settings, 1, "Settings"),
              ),
              const SizedBox(width: 16),
              FocusTraversalOrder(
                order: const NumericFocusOrder(3),
                child: _buildProfileAvatar(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionIcon(IconData icon, int index, String label) {
    return DirectionalFocusWidget(
      debugLabel: label,
      borderRadius: BorderRadius.circular(25),
      padding: const EdgeInsets.all(1),
      onPressed: () {
        print("$label pressed");

        // Navigate to appropriate screen
        if (label == "Search") {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SearchScreen()),
          );
        } else if (label == "Settings") {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SettingsScreen()),
          );
        }
      },
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: const Color(0xFF2D2D44),
          borderRadius: BorderRadius.circular(25),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildProfileAvatar() {
    // Check if current profile has emoji avatar
    final isEmoji = _currentProfile?.avatarPath != null &&
                   _currentProfile!.avatarPath.length <= 4 &&
                   !_currentProfile!.avatarPath.contains('avatar');

    return DirectionalFocusWidget(
      debugLabel: "Profile",
      borderRadius: BorderRadius.circular(25),
      padding: const EdgeInsets.all(1),
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const ProfileSelectionScreen(),
          ),
        ).then((_) => _loadCurrentProfile()); // Reload profile when returning
      },
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          gradient: _currentProfile?.isKidsProfile == true
              ? const LinearGradient(
                  colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : const LinearGradient(
                  colors: [Color(0xFF2196F3), Color(0xFF42A5F5)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 2,
          ),
        ),
        child: Center(
          child: _currentProfile == null
              ? const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 24,
                )
              : Text(
                  isEmoji
                      ? _currentProfile!.avatarPath
                      : _currentProfile!.name.isNotEmpty
                          ? _currentProfile!.name[0].toUpperCase()
                          : '?',
                  style: TextStyle(
                    fontSize: isEmoji ? 24 : 18,
                    color: isEmoji ? null : Colors.white,
                    fontWeight: isEmoji ? null : FontWeight.bold,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildCategoriesGrid() {
    return FocusTraversalGroup(
      policy: OrderedTraversalPolicy(),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Calculate card width: 20% of available width
          double cardWidth = (constraints.maxWidth - (4 * 15)) / 5; // 4 gaps of 15px between 5 cards

          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: _categories.asMap().entries.map((entry) {
              int index = entry.key;
              CategoryItem category = entry.value;
              return SizedBox(
                width: cardWidth,
                child: FocusTraversalOrder(
                  order: NumericFocusOrder(10 + index.toDouble()),
                  child: _buildCategoryCard(category, index),
                ),
              );
            }).toList(),
          );
        },
      ),
    );
  }

  Widget _buildCategoryCard(CategoryItem category, int index) {
    return DirectionalFocusWidget(
      autofocus: index == 0,
      debugLabel: "Category ${category.title}",
      borderRadius: BorderRadius.circular(12),
      padding: const EdgeInsets.all(1),
      onPressed: () {
        print("Category ${category.title} pressed");
        setState(() {
          _selectedCategoryIndex = index;
        });

        // Navigate to appropriate screen based on category
        if (category.title == 'Movies') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const MoviesScreen(),
            ),
          );
        } else {
          // TODO: Add navigation for other categories
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${category.title} coming soon!'),
              backgroundColor: category.color,
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF2D2D44),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0xFF3D3D5C),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(18),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: category.color,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  category.icon,
                  color: Colors.white,
                  size: 30,
                ),
              ),

              const SizedBox(height: 14),

              Text(
                category.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 15,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 6),

              Text(
                category.subtitle,
                style: const TextStyle(
                  color: Color(0xFF8E8E93),
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContinueWatching() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Continue watching',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 20),

        Expanded(
          child: FocusTraversalGroup(
            policy: OrderedTraversalPolicy(),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Calculate card width: 20% of available width with 15px spacing
                double cardWidth = (constraints.maxWidth - (4 * 15)) / 5; // 4 gaps of 15px between 5 cards

                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: _currentProfile?.continueWatching.isNotEmpty == true
                      ? _currentProfile!.continueWatching.take(5).map((item) {
                          final index = _currentProfile!.continueWatching.indexOf(item);
                          return SizedBox(
                            width: cardWidth,
                            child: FocusTraversalOrder(
                              order: NumericFocusOrder(20 + index.toDouble()),
                              child: _buildContinueCard(item, index),
                            ),
                          );
                        }).toList()
                      : List.generate(1, (index) {
                          return SizedBox(
                            width: cardWidth,
                            child: _buildEmptyContinueCard(),
                          );
                        }),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContinueCard(ContinueWatchingItem item, int index) {
    return DirectionalFocusWidget(
      debugLabel: "Continue watching ${item.title}",
      borderRadius: BorderRadius.circular(8),
      padding: const EdgeInsets.all(1),
      onPressed: () {
        print("Continue watching ${item.title} pressed");
        _resumeWatching(item);
      },
      child: AspectRatio(
        aspectRatio: 16 / 9, // 16:9 aspect ratio
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A3E),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFF3A3A5C), width: 1),
          ),
          child: Stack(
            children: [
              // Thumbnail background
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: const Color(0xFF1A1A2E),
                ),
                child: item.thumbnailUrl.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          item.thumbnailUrl,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return const Center(
                              child: Icon(
                                Icons.movie,
                                size: 40,
                                color: Color(0xFF8E8E93),
                              ),
                            );
                          },
                        ),
                      )
                    : const Center(
                        child: Icon(
                          Icons.movie,
                          size: 40,
                          color: Color(0xFF8E8E93),
                        ),
                      ),
              ),

              // Progress bar at bottom
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: LinearProgressIndicator(
                    value: item.totalDuration > 0 ? item.watchedDuration / item.totalDuration : 0.0,
                    backgroundColor: const Color(0xFF3A3A5C),
                    valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF00CED1)),
                  ),
                ),
              ),

              // Play icon overlay
              const Center(
                child: Icon(
                  Icons.play_circle_outline,
                  size: 50,
                  color: Colors.white,
                ),
              ),

              // Movie title at bottom
              Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Text(
                  item.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black,
                      ),
                    ],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyContinueCard() {
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF2A2A3E),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFF3A3A5C), width: 1),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.play_circle_outline,
                size: 40,
                color: Color(0xFF8E8E93),
              ),
              SizedBox(height: 8),
              Text(
                'No items to continue',
                style: TextStyle(
                  color: Color(0xFF8E8E93),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _resumeWatching(ContinueWatchingItem item) {
    // Navigate to ExoPlayer with resume position
    final progressPercent = item.totalDuration > 0
        ? (item.watchedDuration / item.totalDuration * 100).round()
        : 0;
    print("Resuming ${item.title} at $progressPercent%");

    // TODO: Navigate to ExoPlayer with the movie URL and resume position
    // This would require getting the movie URL from the API using item.streamId
    // and passing the currentPosition to ExoPlayer
  }
}

class CategoryItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;

  CategoryItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });
}
