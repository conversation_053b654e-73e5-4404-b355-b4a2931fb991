<variant
    name="release"
    package="com.example.iptv_player"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0;C:\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58b2fa97deb6620e81e8f2146e68c814\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\tmp\kotlin-classes\release;C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\kotlinToolingMetadata;C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.iptv_player"
      generatedSourceFolders="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\Users\<USER>\Documents\augment-projects\FlutterNetflixAndroidtv\iptv_player\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58b2fa97deb6620e81e8f2146e68c814\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
