import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../theme/app_theme.dart';
import '../models/channel.dart';
import 'simple_tv_focus.dart';

class HeroBanner extends StatelessWidget {
  final Channel channel;
  final VoidCallback? onPlayPressed;
  final VoidCallback? onInfoPressed;

  const HeroBanner({
    Key? key,
    required this.channel,
    this.onPlayPressed,
    this.onInfoPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 500,
      width: double.infinity,
      child: Stack(
        children: [
          // Background Image
          Positioned.fill(
            child: channel.logoUrl != null
                ? CachedNetworkImage(
                    imageUrl: channel.logoUrl!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppTheme.cardBackground,
                      child: const Center(
                        child: CircularProgressIndicator(
                          color: AppTheme.primaryRed,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: AppTheme.cardBackground,
                      child: const Icon(
                        Icons.tv,
                        size: 100,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  )
                : Container(
                    color: AppTheme.cardBackground,
                    child: const Icon(
                      Icons.tv,
                      size: 100,
                      color: AppTheme.textSecondary,
                    ),
                  ),
          ),
          
          // Gradient Overlay
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerRight,
                  end: Alignment.centerLeft,
                  colors: [
                    Colors.transparent,
                    AppTheme.darkBackground.withOpacity(0.7),
                    AppTheme.darkBackground,
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
            ),
          ),
          
          // Content
          Positioned(
            left: 60,
            bottom: 80,
            child: SizedBox(
              width: 500,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Channel Name
                  Text(
                    channel.name,
                    style: Theme.of(context).textTheme.displayLarge?.copyWith(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.8),
                          offset: const Offset(2, 2),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Description
                  if (channel.description != null)
                    Text(
                      channel.description!,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontSize: 18,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.8),
                            offset: const Offset(1, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  
                  const SizedBox(height: 24),
                  
                  // Action Buttons
                  Row(
                    children: [
                      // Play Button
                      TVButton(
                        text: 'Play',
                        icon: Icons.play_arrow,
                        autofocus: true,
                        onPressed: onPlayPressed,
                      ),

                      const SizedBox(width: 16),

                      // Info Button
                      TVButton(
                        text: 'More Info',
                        icon: Icons.info_outline,
                        backgroundColor: Colors.white.withOpacity(0.2),
                        onPressed: onInfoPressed,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // Category Badge
          Positioned(
            left: 60,
            top: 60,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.primaryRed.withOpacity(0.9),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                channel.category.toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
