package com.example.iptv_player

import android.content.Intent
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.iptv_player/exoplayer"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "launchExoPlayer" -> {
                    val streamUrl = call.argument<String>("streamUrl")
                    val movieTitle = call.argument<String>("movieTitle")
                    val movieId = call.argument<String>("movieId")
                    val movieThumbnail = call.argument<String>("movieThumbnail")
                    val resumePosition = call.argument<Int>("resumePosition")?.toLong()

                    if (streamUrl != null) {
                        launchExoPlayer(streamUrl, movieTitle ?: "Unknown Movie", movieId, movieThumbnail, resumePosition)
                        result.success(true)
                    } else {
                        result.error("INVALID_ARGUMENT", "Stream URL is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun launchExoPlayer(streamUrl: String, movieTitle: String, movieId: String?, movieThumbnail: String?, resumePosition: Long?) {
        val intent = Intent(this, ExoPlayerActivity::class.java).apply {
            putExtra(ExoPlayerActivity.EXTRA_STREAM_URL, streamUrl)
            putExtra(ExoPlayerActivity.EXTRA_MOVIE_TITLE, movieTitle)
            putExtra("movie_id", movieId)
            putExtra("movie_thumbnail", movieThumbnail)
            putExtra("resume_position", resumePosition ?: 0L)
        }
        startActivity(intent)
    }
}
