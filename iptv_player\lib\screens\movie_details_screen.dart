import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../widgets/directional_focus_widget.dart';
import '../models/xtreme_api_models.dart';
import '../services/xtreme_api_service.dart';
import '../services/local_database_service.dart';

class MovieDetailsScreen extends StatefulWidget {
  final VodContent movie;

  const MovieDetailsScreen({
    Key? key,
    required this.movie,
  }) : super(key: key);

  @override
  State<MovieDetailsScreen> createState() => _MovieDetailsScreenState();
}

class _MovieDetailsScreenState extends State<MovieDetailsScreen> {
  final XtremeApiService _apiService = XtremeApiService();
  final LocalDatabaseService _dbService = LocalDatabaseService();
  
  bool _isFavorite = false;
  bool _isLoading = false;
  Map<String, dynamic>? _movieInfo;

  @override
  void initState() {
    super.initState();
    _checkFavoriteStatus();
    _loadMovieInfo();
  }

  Future<void> _checkFavoriteStatus() async {
    try {
      final profile = await _dbService.getCurrentProfile();
      if (profile != null) {
        setState(() {
          _isFavorite = profile.favoriteMovies.contains(widget.movie.streamId.toString());
        });
      }
    } catch (e) {
      print('Error checking favorite status: $e');
    }
  }

  Future<void> _loadMovieInfo() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final info = await _apiService.getVodInfo(widget.movie.streamId);
      
      setState(() {
        _movieInfo = info;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Error loading movie info: $e');
    }
  }

  Future<void> _toggleFavorite() async {
    try {
      final profile = await _dbService.getCurrentProfile();
      if (profile == null) return;

      final movieId = widget.movie.streamId.toString();
      List<String> updatedFavorites = List.from(profile.favoriteMovies);

      if (_isFavorite) {
        updatedFavorites.remove(movieId);
      } else {
        updatedFavorites.add(movieId);
      }

      final updatedProfile = profile.copyWith(favoriteMovies: updatedFavorites);
      await _dbService.updateProfile(updatedProfile);

      setState(() {
        _isFavorite = !_isFavorite;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isFavorite ? 'Added to My List' : 'Removed from My List',
          ),
          backgroundColor: const Color(0xFFE17055),
        ),
      );
    } catch (e) {
      print('Error toggling favorite: $e');
    }
  }

  void _playMovie() {
    final streamUrl = _apiService.getVodStreamUrl(
      widget.movie.streamId,
      widget.movie.containerExtension,
    );
    
    // TODO: Implement video player
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Playing: ${widget.movie.name}'),
        backgroundColor: const Color(0xFFE17055),
      ),
    );
    
    print('🎬 Playing movie: ${widget.movie.name}');
    print('🔗 Stream URL: $streamUrl');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: Stack(
        children: [
          // Background image
          if (widget.movie.streamIcon.isNotEmpty)
            Positioned.fill(
              child: CachedNetworkImage(
                imageUrl: widget.movie.streamIcon,
                fit: BoxFit.cover,
                color: Colors.black.withOpacity(0.7),
                colorBlendMode: BlendMode.darken,
                errorWidget: (context, url, error) => Container(
                  color: const Color(0xFF1A1A2E),
                ),
              ),
            ),
          
          // Content
          SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: _buildContent(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(40),
      child: Row(
        children: [
          // Back button
          DirectionalFocusWidget(
            debugLabel: "Back",
            borderRadius: BorderRadius.circular(12),
            onPressed: () => Navigator.pop(context),
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
          
          const Spacer(),
          
          // Favorite button
          DirectionalFocusWidget(
            debugLabel: "Favorite",
            borderRadius: BorderRadius.circular(12),
            onPressed: _toggleFavorite,
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                _isFavorite ? Icons.favorite : Icons.favorite_border,
                color: _isFavorite ? Colors.red : Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(40),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Movie poster
          Container(
            width: 300,
            height: 450,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.5),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: widget.movie.streamIcon.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: widget.movie.streamIcon,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: const Color(0xFF3D3D5C),
                        child: const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: const Color(0xFF3D3D5C),
                        child: const Center(
                          child: Icon(
                            Icons.movie,
                            color: Colors.white54,
                            size: 64,
                          ),
                        ),
                      ),
                    )
                  : Container(
                      color: const Color(0xFF3D3D5C),
                      child: const Center(
                        child: Icon(
                          Icons.movie,
                          color: Colors.white54,
                          size: 64,
                        ),
                      ),
                    ),
            ),
          ),
          
          const SizedBox(width: 40),
          
          // Movie details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  widget.movie.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Movie info
                if (_movieInfo != null) ...[
                  _buildMovieInfoRow('Year', _movieInfo!['releasedate'] ?? 'Unknown'),
                  _buildMovieInfoRow('Genre', _movieInfo!['genre'] ?? 'Unknown'),
                  _buildMovieInfoRow('Rating', _movieInfo!['rating'] ?? 'Not rated'),
                  _buildMovieInfoRow('Duration', _movieInfo!['duration'] ?? 'Unknown'),
                  
                  const SizedBox(height: 20),
                  
                  // Description
                  if (_movieInfo!['plot'] != null && _movieInfo!['plot'].isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Description',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          _movieInfo!['plot'],
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 16,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                ],
                
                const SizedBox(height: 40),
                
                // Action buttons
                Row(
                  children: [
                    // Play button
                    DirectionalFocusWidget(
                      autofocus: true,
                      debugLabel: "Play",
                      borderRadius: BorderRadius.circular(8),
                      onPressed: _playMovie,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE17055),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 24,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Play',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 20),
                    
                    // Add to list button
                    DirectionalFocusWidget(
                      debugLabel: "Add to List",
                      borderRadius: BorderRadius.circular(8),
                      onPressed: _toggleFavorite,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _isFavorite ? Icons.check : Icons.add,
                              color: Colors.white,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _isFavorite ? 'In My List' : 'My List',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMovieInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
