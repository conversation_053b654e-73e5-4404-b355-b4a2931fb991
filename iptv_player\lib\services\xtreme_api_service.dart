import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/xtreme_api_models.dart';

class XtremeApiService {
  static const String baseUrl = 'https://tvfajeriptv.com';
  
  String? _username;
  String? _password;
  XtremeAuthResponse? _authResponse;

  // Singleton pattern
  static final XtremeApiService _instance = XtremeApiService._internal();
  factory XtremeApiService() => _instance;
  XtremeApiService._internal();

  // Authentication
  Future<bool> authenticate(String username, String password) async {
    try {
      _username = username;
      _password = password;

      final url = '$baseUrl/player_api.php?username=$username&password=$password';
      print('🔐 Authenticating with: $url');

      final response = await http.get(Uri.parse(url));

      print('📡 Response Status: ${response.statusCode}');
      print('📡 Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('📊 Parsed Data: $data');

        if (data['user_info'] != null && (data['user_info']['auth'] == 1 || data['user_info']['auth'] == '1')) {
          // For now, just create a simple auth response without full parsing
          _authResponse = XtremeAuthResponse(
            userInfo: UserInfo(
              username: data['user_info']['username'] ?? '',
              password: data['user_info']['password'] ?? '',
              message: data['user_info']['message'] ?? '',
              auth: data['user_info']['auth']?.toString() ?? '',
              status: data['user_info']['status'] ?? '',
              expDate: data['user_info']['exp_date'] ?? '',
              isTrial: data['user_info']['is_trial'] == '1',
              activeCons: int.tryParse(data['user_info']['active_cons']?.toString() ?? '0') ?? 0,
              createdAt: data['user_info']['created_at'] ?? '',
              maxConnections: int.tryParse(data['user_info']['max_connections']?.toString() ?? '0') ?? 0,
              allowedOutputFormats: List<String>.from(data['user_info']['allowed_output_formats'] ?? []),
            ),
            serverInfo: ServerInfo(
              url: data['server_info']['url'] ?? '',
              port: data['server_info']['port'] ?? '',
              httpsPort: data['server_info']['https_port'] ?? '',
              serverProtocol: data['server_info']['server_protocol'] ?? '',
              rtmpPort: data['server_info']['rtmp_port'] ?? '',
              timezone: data['server_info']['timezone'] ?? '',
              timestampNow: int.tryParse(data['server_info']['timestamp_now']?.toString() ?? '0') ?? 0,
              timeNow: data['server_info']['time_now'] ?? '',
            ),
          );
          print('✅ Authentication successful for user: ${data['user_info']['username']}');
          return true;
        } else {
          print('❌ Authentication failed: Invalid credentials or auth != 1');
          print('❌ User info: ${data['user_info']}');
          return false;
        }
      } else {
        print('❌ Authentication failed: HTTP ${response.statusCode}');
        print('❌ Response body: ${response.body}');
        return false;
      }
    } catch (e) {
      print('❌ Authentication error: $e');
      return false;
    }
  }

  // Check if user is authenticated
  bool get isAuthenticated => _authResponse != null && _username != null && _password != null;

  // Get user info
  XtremeAuthResponse? get authResponse => _authResponse;

  // Get Live TV Categories
  Future<List<IptvCategory>> getLiveTvCategories() async {
    if (!isAuthenticated) throw Exception('Not authenticated');

    try {
      final url = '$baseUrl/player_api.php?username=$_username&password=$_password&action=get_live_categories';
      print('📺 Fetching live TV categories...');

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final categories = data.map((json) => IptvCategory.fromJson(json)).toList();
        print('✅ Fetched ${categories.length} live TV categories');
        return categories;
      } else {
        throw Exception('Failed to load live TV categories');
      }
    } catch (e) {
      print('❌ Error fetching live TV categories: $e');
      rethrow;
    }
  }

  // Get Live Channels
  Future<List<LiveChannel>> getLiveChannels({String? categoryId}) async {
    if (!isAuthenticated) throw Exception('Not authenticated');

    try {
      String url = '$baseUrl/player_api.php?username=$_username&password=$_password&action=get_live_streams';
      if (categoryId != null) {
        url += '&category_id=$categoryId';
      }
      
      print('📺 Fetching live channels...');

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final channels = data.map((json) => LiveChannel.fromJson(json)).toList();
        print('✅ Fetched ${channels.length} live channels');
        return channels;
      } else {
        throw Exception('Failed to load live channels');
      }
    } catch (e) {
      print('❌ Error fetching live channels: $e');
      rethrow;
    }
  }

  // Get VOD Categories
  Future<List<IptvCategory>> getVodCategories() async {
    if (!isAuthenticated) throw Exception('Not authenticated');

    try {
      final url = '$baseUrl/player_api.php?username=$_username&password=$_password&action=get_vod_categories';
      print('🎬 Fetching VOD categories...');

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final categories = data.map((json) => IptvCategory.fromJson(json)).toList();
        print('✅ Fetched ${categories.length} VOD categories');
        return categories;
      } else {
        throw Exception('Failed to load VOD categories');
      }
    } catch (e) {
      print('❌ Error fetching VOD categories: $e');
      rethrow;
    }
  }

  // Get VOD Content (Movies)
  Future<List<VodContent>> getVodContent({String? categoryId}) async {
    if (!isAuthenticated) throw Exception('Not authenticated');

    try {
      String url = '$baseUrl/player_api.php?username=$_username&password=$_password&action=get_vod_streams';
      if (categoryId != null) {
        url += '&category_id=$categoryId';
      }
      
      print('🎬 Fetching VOD content...');

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final content = data.map((json) => VodContent.fromJson(json)).toList();
        print('✅ Fetched ${content.length} VOD items');
        return content;
      } else {
        throw Exception('Failed to load VOD content');
      }
    } catch (e) {
      print('❌ Error fetching VOD content: $e');
      rethrow;
    }
  }

  // Get detailed VOD information
  Future<Map<String, dynamic>> getVodInfo(int vodId) async {
    if (!isAuthenticated) throw Exception('Not authenticated');

    try {
      final url = '$baseUrl/player_api.php?username=$_username&password=$_password&action=get_vod_info&vod_id=$vodId';

      print('🎬 Fetching VOD info for ID: $vodId');
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        print('✅ Fetched VOD info for: ${data['info']?['name'] ?? 'Unknown'}');
        return data;
      } else {
        throw Exception('Failed to load VOD info: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error fetching VOD info: $e');
      throw Exception('Failed to fetch VOD info: $e');
    }
  }

  // Get VOD stream URL for video playback (MP4 format with redirect)
  String getVodStreamUrl(int streamId, String containerExtension) {
    if (!isAuthenticated) throw Exception('Not authenticated');

    // Use original MP4 format - server will redirect to actual streaming URL
    final streamUrl = '$baseUrl/movie/$_username/$_password/$streamId.$containerExtension';
    print('📺 Generated stream URL: $streamUrl');
    return streamUrl;
  }

  // Get VOD stream URL with specific format
  String getVodStreamUrlWithFormat(int streamId, String format) {
    if (!isAuthenticated) throw Exception('Not authenticated');

    final streamUrl = '$baseUrl/movie/$_username/$_password/$streamId.$format';
    print('📺 Generated stream URL ($format): $streamUrl');
    return streamUrl;
  }

  // Get Series Categories
  Future<List<IptvCategory>> getSeriesCategories() async {
    if (!isAuthenticated) throw Exception('Not authenticated');

    try {
      final url = '$baseUrl/player_api.php?username=$_username&password=$_password&action=get_series_categories';
      print('📺 Fetching series categories...');

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final categories = data.map((json) => IptvCategory.fromJson(json)).toList();
        print('✅ Fetched ${categories.length} series categories');
        return categories;
      } else {
        throw Exception('Failed to load series categories');
      }
    } catch (e) {
      print('❌ Error fetching series categories: $e');
      rethrow;
    }
  }

  // Get Series Content
  Future<List<VodContent>> getSeriesContent({String? categoryId}) async {
    if (!isAuthenticated) throw Exception('Not authenticated');

    try {
      String url = '$baseUrl/player_api.php?username=$_username&password=$_password&action=get_series';
      if (categoryId != null) {
        url += '&category_id=$categoryId';
      }
      
      print('📺 Fetching series content...');

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final content = data.map((json) => VodContent.fromJson(json)).toList();
        print('✅ Fetched ${content.length} series');
        return content;
      } else {
        throw Exception('Failed to load series content');
      }
    } catch (e) {
      print('❌ Error fetching series content: $e');
      rethrow;
    }
  }

  // Get Stream URL for Live Channel
  String getLiveStreamUrl(int streamId, {String format = 'ts'}) {
    if (!isAuthenticated) throw Exception('Not authenticated');
    return '$baseUrl/live/$_username/$_password/$streamId.$format';
  }

  // Get Stream URL for Series Episode
  String getSeriesStreamUrl(int streamId, String containerExtension) {
    if (!isAuthenticated) throw Exception('Not authenticated');
    return '$baseUrl/series/$_username/$_password/$streamId.$containerExtension';
  }

  // Get EPG for channel
  Future<List<EpgData>> getEpg(int streamId) async {
    if (!isAuthenticated) throw Exception('Not authenticated');

    try {
      final url = '$baseUrl/player_api.php?username=$_username&password=$_password&action=get_short_epg&stream_id=$streamId';
      print('📅 Fetching EPG for stream $streamId...');

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> epgList = data['epg_listings'] ?? [];
        final epgData = epgList.map((json) => EpgData.fromJson(json)).toList();
        print('✅ Fetched ${epgData.length} EPG entries');
        return epgData;
      } else {
        throw Exception('Failed to load EPG data');
      }
    } catch (e) {
      print('❌ Error fetching EPG: $e');
      rethrow;
    }
  }

  // Logout
  void logout() {
    _username = null;
    _password = null;
    _authResponse = null;
    print('👋 Logged out');
  }
}
