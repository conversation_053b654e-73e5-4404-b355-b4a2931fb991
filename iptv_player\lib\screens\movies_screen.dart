import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/directional_focus_widget.dart';
import '../widgets/tv_navigation_widget.dart';
import '../models/user_profile.dart';
import '../models/xtreme_api_models.dart';
import '../services/local_database_service.dart';
import '../services/xtreme_api_service.dart';
import 'settings_screen.dart';
import 'search_screen.dart';
import 'profile_selection_screen.dart';

// Custom focus widget for movies screen - NO SCALE, only border
class MoviesFocusWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final String? debugLabel;
  final BorderRadius? borderRadius;
  final bool autofocus;

  const MoviesFocusWidget({
    Key? key,
    required this.child,
    this.onPressed,
    this.debugLabel,
    this.borderRadius,
    this.autofocus = false,
  }) : super(key: key);

  @override
  State<MoviesFocusWidget> createState() => _MoviesFocusWidgetState();
}

class _MoviesFocusWidgetState extends State<MoviesFocusWidget> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode(debugLabel: widget.debugLabel);
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });

    if (_isFocused) {
      HapticFeedback.selectionClick();
      print("Focus gained: ${widget.debugLabel ?? 'Unknown'}");
    } else {
      print("Focus lost: ${widget.debugLabel ?? 'Unknown'}");
    }
  }

  void _handlePress() {
    HapticFeedback.lightImpact();
    print("Pressed: ${widget.debugLabel ?? 'Unknown'}");
    widget.onPressed?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          // Handle directional navigation
          if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
            print("Moving focus left from ${widget.debugLabel}");
            _focusNode.focusInDirection(TraversalDirection.left);
            return KeyEventResult.handled;
          }

          if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
            print("Moving focus right from ${widget.debugLabel}");
            _focusNode.focusInDirection(TraversalDirection.right);
            return KeyEventResult.handled;
          }

          if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
            print("Moving focus down from ${widget.debugLabel}");
            _focusNode.focusInDirection(TraversalDirection.down);
            return KeyEventResult.handled;
          }

          if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
            print("Moving focus up from ${widget.debugLabel}");
            _focusNode.focusInDirection(TraversalDirection.up);
            return KeyEventResult.handled;
          }

          // Handle selection
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter) {
            _handlePress();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: _handlePress,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: _isFocused ? Colors.white : Colors.transparent,
              width: 2,
            ),
            borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
          ),
          child: widget.child,
        ),
      ),
    );
  }
}



class MoviesScreen extends StatefulWidget {
  const MoviesScreen({Key? key}) : super(key: key);

  @override
  State<MoviesScreen> createState() => _MoviesScreenState();
}

class _MoviesScreenState extends State<MoviesScreen> {
  final LocalDatabaseService _dbService = LocalDatabaseService();
  final XtremeApiService _apiService = XtremeApiService();

  UserProfile? _currentProfile;
  List<IptvCategory> _categories = [];
  List<VodContent> _movies = [];
  List<VodContent> _favoriteMovies = [];

  bool _isLoadingCategories = true;
  bool _isLoadingMovies = false;
  String? _error;

  int _selectedCategoryIndex = 0;
  int _selectedMovieIndex = 0;
  String _selectedCategoryType = 'favorites'; // 'favorites' or 'category'
  IptvCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _loadCurrentProfile();
    _loadCategories();
    _loadFavoriteMovies();
  }

  Future<void> _loadCurrentProfile() async {
    try {
      final profile = await _dbService.getCurrentProfile();
      setState(() {
        _currentProfile = profile;
      });
    } catch (e) {
      print('Error loading current profile: $e');
    }
  }

  Future<void> _loadCategories() async {
    try {
      setState(() {
        _isLoadingCategories = true;
        _error = null;
      });

      final categories = await _apiService.getVodCategories();

      setState(() {
        _categories = categories;
        _isLoadingCategories = false;
        // Start with favorites selected
        _selectedCategoryType = 'favorites';
        _selectedCategoryIndex = 0;
      });

      print('📽️ Loaded ${categories.length} movie categories');
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoadingCategories = false;
      });
      print('❌ Error loading movie categories: $e');
    }
  }

  Future<void> _loadFavoriteMovies() async {
    try {
      if (_currentProfile == null) return;

      setState(() {
        _isLoadingMovies = true;
      });

      // Get all favorite movie IDs from profile
      final favoriteIds = _currentProfile!.favoriteMovies;
      List<VodContent> favorites = [];

      // For each favorite ID, we need to get the movie details
      // This is a simplified approach - in a real app you might cache this data
      for (String movieId in favoriteIds) {
        try {
          // You might need to implement a method to get movie by ID
          // For now, we'll show a placeholder
          print('Loading favorite movie: $movieId');
        } catch (e) {
          print('Error loading favorite movie $movieId: $e');
        }
      }

      setState(() {
        _favoriteMovies = favorites;
        _movies = favorites; // Show favorites by default
        _isLoadingMovies = false;
      });

      print('❤️ Loaded ${favorites.length} favorite movies');
    } catch (e) {
      setState(() {
        _isLoadingMovies = false;
      });
      print('❌ Error loading favorite movies: $e');
    }
  }

  Future<void> _loadMoviesForCategory(IptvCategory category) async {
    try {
      setState(() {
        _isLoadingMovies = true;
        _selectedCategory = category;
        _selectedCategoryType = 'category';
        _selectedMovieIndex = 0;
      });

      final movies = await _apiService.getVodContent(categoryId: category.categoryId);

      setState(() {
        _movies = movies;
        _isLoadingMovies = false;
      });

      print('🎬 Loaded ${movies.length} movies for ${category.categoryName}');
    } catch (e) {
      setState(() {
        _isLoadingMovies = false;
      });
      print('❌ Error loading movies: $e');
    }
  }

  void _onCategorySelected(int index) {
    if (index == 0) {
      // Favorites selected
      setState(() {
        _selectedCategoryIndex = 0;
        _selectedCategoryType = 'favorites';
        _movies = _favoriteMovies;
        _selectedMovieIndex = 0;
      });
      print('❤️ Selected Favorites');
    } else if (index - 1 < _categories.length) {
      // Regular category selected (subtract 1 because favorites is at index 0)
      setState(() {
        _selectedCategoryIndex = index;
      });
      _loadMoviesForCategory(_categories[index - 1]);
    }
  }

  void _onMovieSelected(VodContent movie) {
    // TODO: Navigate to movie details
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected: ${movie.name}'),
        backgroundColor: const Color(0xFFE17055),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Row(
              children: [
                _buildCategoriesSidebar(),
                Expanded(
                  child: _buildMoviesContent(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
      child: Row(
        children: [
          // Logo and title
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.asset(
                    'assets/images/logo.png',
                    width: 60,
                    height: 60,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFFB347), Color(0xFFFF6B35)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Center(
                          child: Text(
                            'GO',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                'Movies',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const Spacer(),

          // Action Icons (Search, Settings, Profile - right to left)
          Row(
            children: [
              _buildActionIcon(Icons.search, "Search", () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const SearchScreen()),
                );
              }),
              const SizedBox(width: 16),
              _buildActionIcon(Icons.settings, "Settings", () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const SettingsScreen()),
                );
              }),
              const SizedBox(width: 16),
              _buildProfileAvatar(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionIcon(IconData icon, String label, VoidCallback onPressed) {
    return DirectionalFocusWidget(
      debugLabel: label,
      borderRadius: BorderRadius.circular(25),
      onPressed: onPressed,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: const Color(0xFF2D2D44),
          borderRadius: BorderRadius.circular(25),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildProfileAvatar() {
    // Check if current profile has emoji avatar
    final isEmoji = _currentProfile?.avatarPath != null &&
                   _currentProfile!.avatarPath.length <= 4 &&
                   !_currentProfile!.avatarPath.contains('avatar');

    return DirectionalFocusWidget(
      debugLabel: "Profile",
      borderRadius: BorderRadius.circular(25),
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ProfileSelectionScreen()),
        );
      },
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isEmoji ? Colors.transparent : const Color(0xFF2D2D44),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: const Color(0xFF3D3D5C),
            width: 2,
          ),
        ),
        child: isEmoji
            ? Center(
                child: Text(
                  _currentProfile!.avatarPath,
                  style: const TextStyle(fontSize: 24),
                ),
              )
            : const Icon(
                Icons.person,
                color: Colors.white,
                size: 24,
              ),
      ),
    );
  }

  Widget _buildCategoriesSidebar() {
    return Container(
      width: 300,
      child: Row(
        children: [
          // Categories list
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 20, right: 10, top: 10, bottom: 20),
              child: _isLoadingCategories
                  ? const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _categories.length + 1, // +1 for Favorites
                      itemBuilder: (context, index) {
                        String categoryName;
                        IconData categoryIcon;
                        bool isSelected = index == _selectedCategoryIndex;

                        if (index == 0) {
                          // Favorites category
                          categoryName = 'My List';
                          categoryIcon = Icons.favorite;
                        } else {
                          // Regular categories
                          final category = _categories[index - 1];
                          categoryName = category.categoryName;
                          categoryIcon = Icons.movie;
                        }

                        return Container(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: MoviesFocusWidget(
                            autofocus: index == 0,
                            debugLabel: "Category $categoryName",
                            borderRadius: BorderRadius.circular(8),
                            onPressed: () => _onCategorySelected(index),
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? const Color(0xFFE17055)
                                    : const Color(0xFF3D3D5C),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    categoryIcon,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      categoryName,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  // Show count for favorites
                                  if (index == 0 && _favoriteMovies.isNotEmpty)
                                    Container(
                                      margin: const EdgeInsets.only(left: 8),
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Text(
                                        '${_favoriteMovies.length}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ),

          // Beautiful gradient divider
          Container(
            width: 2,
            margin: const EdgeInsets.symmetric(vertical: 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  const Color(0xFF3D3D5C).withOpacity(0.3),
                  const Color(0xFF3D3D5C),
                  const Color(0xFF3D3D5C).withOpacity(0.3),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.1, 0.5, 0.9, 1.0], // Smaller transparent areas
              ),
              borderRadius: BorderRadius.circular(1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMoviesContent() {
    return _buildMoviesGrid();
  }

  Widget _buildMoviesGrid() {
    if (_isLoadingMovies) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
            ),
            SizedBox(height: 20),
            Text(
              'Loading movies...',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    if (_movies.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _selectedCategoryType == 'favorites' ? Icons.favorite_border : Icons.movie_outlined,
              color: Colors.white54,
              size: 64,
            ),
            const SizedBox(height: 20),
            Text(
              _selectedCategoryType == 'favorites'
                  ? 'No movies in your list'
                  : 'No movies in this category',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              _selectedCategoryType == 'favorites'
                  ? 'Add movies to your favorites to see them here'
                  : 'Try selecting another category',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(20), // Simple padding without scale concerns
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 5,
          childAspectRatio: 0.7,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _movies.length,
        itemBuilder: (context, index) {
        final movie = _movies[index];

        return MoviesFocusWidget(
          debugLabel: "Movie ${movie.name}",
          borderRadius: BorderRadius.circular(12),
          onPressed: () => _onMovieSelected(movie),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Movie poster
                  movie.streamIcon.isNotEmpty
                      ? Image.network(
                          movie.streamIcon,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: const Color(0xFF3D3D5C),
                              child: const Center(
                                child: Icon(
                                  Icons.movie,
                                  color: Colors.white54,
                                  size: 48,
                                ),
                              ),
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              color: const Color(0xFF3D3D5C),
                              child: const Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE17055)),
                                ),
                              ),
                            );
                          },
                        )
                      : Container(
                          color: const Color(0xFF3D3D5C),
                          child: const Center(
                            child: Icon(
                              Icons.movie,
                              color: Colors.white54,
                              size: 48,
                            ),
                          ),
                        ),

                  // Gradient overlay
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 60, // Reduced height to prevent overflow
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.8),
                          ],
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8), // Reduced padding
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              movie.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 13, // Slightly smaller font
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            // Removed the date/number display
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      ),
    );
  }
}
